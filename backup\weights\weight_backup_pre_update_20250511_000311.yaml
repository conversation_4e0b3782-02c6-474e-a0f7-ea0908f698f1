features:
  normalize_features: true
  use_cross_position: true
  use_markov: true
  window_10: 10
  window_30: 30
  window_5: 5
iteration:
  early_stop_on_hit: true
  feedback_mode: path_tagged
  feedback_trigger_on_zero: true
  max_rounds: 5
logging:
  level: INFO
  log_format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  log_to_file: true
model:
  path: models/xgb_model.json
  threshold: 0.5
  type: xgboost
  use_model: true
paths:
  data_path: data/lottery_history.xlsx
  feedback_save_path: data/feedback
  log_dir: logs
  memory_path: data/memory
  model_dir: models
  result_save_path: data/results
rule_weights:
  cold_b: 0.25
  cold_g: 0.25
  cold_gap30_b: 0.03
  cold_gap30_g: 0.03
  cold_gap30_k: 0.03
  cold_gap30_t: 0.03
  cold_k: 0.25
  cold_t: 0.25
  cross_bg: 0.03
  cross_bt: 0.03
  cross_kb: 0.03
  cross_kg: 0.03
  cross_kt: 0.03
  cross_tg: 0.03
  dist20_b_1: 0.02
  dist20_b_6: 0.02
  dist20_g_3: 0.02
  dist20_g_8: 0.02
  dist20_k_0: 0.02
  dist20_k_5: 0.02
  dist20_t_2: 0.02
  dist20_t_7: 0.02
  freq5_b: 0.04
  freq5_g: 0.04
  freq5_k: 0.04
  freq5_t: 0.04
  hot_b: -0.15
  hot_g: -0.15
  hot_k: -0.15
  hot_t: -0.15
  ma_b_3: 0.02
  ma_b_5: 0.02
  ma_g_3: 0.02
  ma_g_5: 0.02
  ma_k_3: 0.02
  ma_k_5: 0.02
  ma_t_3: 0.02
  ma_t_5: 0.02
  markov_b_1: 0.03
  markov_b_2: 0.03
  markov_g_1: 0.03
  markov_g_2: 0.03
  markov_k_1: 0.03
  markov_k_2: 0.03
  markov_t_1: 0.03
  markov_t_2: 0.03
  period_b: 0.05
  period_g: 0.05
  period_k: 0.05
  period_t: 0.05
  trans_b_0to0: 0.01
  trans_b_1to1: 0.01
  trans_b_2to2: 0.01
  trans_b_3to3: 0.01
  trans_b_4to4: 0.01
  trans_b_5to5: 0.01
  trans_b_6to6: 0.01
  trans_b_7to7: 0.01
  trans_b_8to8: 0.01
  trans_b_9to9: 0.01
  trans_g_0to0: 0.01
  trans_g_1to1: 0.01
  trans_g_2to2: 0.01
  trans_g_3to3: 0.01
  trans_g_4to4: 0.01
  trans_g_5to5: 0.01
  trans_g_6to6: 0.01
  trans_g_7to7: 0.01
  trans_g_8to8: 0.01
  trans_g_9to9: 0.01
  trans_k_0to0: 0.01
  trans_k_1to1: 0.01
  trans_k_2to2: 0.01
  trans_k_3to3: 0.01
  trans_k_4to4: 0.01
  trans_k_5to5: 0.01
  trans_k_6to6: 0.01
  trans_k_7to7: 0.01
  trans_k_8to8: 0.01
  trans_k_9to9: 0.01
  trans_t_0to0: 0.01
  trans_t_1to1: 0.01
  trans_t_2to2: 0.01
  trans_t_3to3: 0.01
  trans_t_4to4: 0.01
  trans_t_5to5: 0.01
  trans_t_6to6: 0.01
  trans_t_7to7: 0.01
  trans_t_8to8: 0.01
  trans_t_9to9: 0.01
  trend10_b: 0.04
  trend10_g: 0.04
  trend10_k: 0.04
  trend10_t: 0.04
scoring:
  model_weight: 0.6
  normalize_scores: true
  rule_weight: 0.4
  score_clip_range:
    max: 1.0
    min: 0.0
selection:
  diversity_enforce: true
  filter_illegal: true
  top_n: 20
strategy_paths:
  allow_random_disturbance: true
  max_active_paths: 2
  min_weight_threshold: 0.1
  冷修复: 0.2
  爆破: 0.3
  稳定结构: 0.25
  高频周期: 0.25
system:
  candidate_count: 1000
  retrain_interval: 5
