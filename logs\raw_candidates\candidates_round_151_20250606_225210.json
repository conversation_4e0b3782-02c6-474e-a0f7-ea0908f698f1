{"round": 151, "timestamp": "2025-06-06T22:52:10.519141", "count": 100, "expected_count": 100, "candidates": ["6662", "5544", "4301", "9015", "5565", "5294", "5457", "0118", "4910", "1780", "0407", "5050", "7709", "6262", "2663", "0867", "3358", "2046", "4021", "7266", "5129", "3447", "5174", "0998", "5422", "1080", "0568", "3140", "2877", "7420", "5944", "8999", "3032", "6915", "3394", "6869", "0891", "7133", "1090", "0088", "0561", "0575", "3101", "0303", "4668", "5167", "7636", "3465", "9421", "0923", "3163", "5764", "3414", "7201", "8411", "4286", "8003", "2450", "3344", "9170", "0820", "5744", "5673", "9137", "1101", "5322", "5018", "7640", "5291", "9810", "9607", "1334", "8191", "4465", "6577", "9900", "6264", "0696", "2680", "6560", "6564", "4040", "1801", "0008", "6263", "7246", "8088", "7281", "4282", "5687", "5685", "7439", "7664", "1910", "6686", "1433", "0089", "9343", "5182", "5469"], "candidates_with_strategy": [{"structure": "6662", "strategy": "均值回归"}, {"structure": "5544", "strategy": "均值回归"}, {"structure": "4301", "strategy": "高频周期"}, {"structure": "9015", "strategy": "极值"}, {"structure": "5565", "strategy": "均值回归"}, {"structure": "5294", "strategy": "高频周期"}, {"structure": "5457", "strategy": "均值回归"}, {"structure": "0118", "strategy": "极值"}, {"structure": "4910", "strategy": "极值"}, {"structure": "1780", "strategy": "极值"}, {"structure": "0407", "strategy": "高频周期"}, {"structure": "5050", "strategy": "均值回归"}, {"structure": "7709", "strategy": "多样化处理"}, {"structure": "6262", "strategy": "多样化处理"}, {"structure": "2663", "strategy": "高频周期"}, {"structure": "0867", "strategy": "多样化处理"}, {"structure": "3358", "strategy": "高频周期"}, {"structure": "2046", "strategy": "多样化处理"}, {"structure": "4021", "strategy": "高频周期"}, {"structure": "7266", "strategy": "均值回归"}, {"structure": "5129", "strategy": "极值"}, {"structure": "3447", "strategy": "多样化处理"}, {"structure": "5174", "strategy": "高频周期"}, {"structure": "0998", "strategy": "极值"}, {"structure": "5422", "strategy": "多样化处理"}, {"structure": "1080", "strategy": "极值"}, {"structure": "0568", "strategy": "多样化处理"}, {"structure": "3140", "strategy": "高频周期"}, {"structure": "2877", "strategy": "多样化处理"}, {"structure": "7420", "strategy": "均值回归"}, {"structure": "5944", "strategy": "均值回归"}, {"structure": "8999", "strategy": "极值"}, {"structure": "3032", "strategy": "高频周期"}, {"structure": "6915", "strategy": "均值回归"}, {"structure": "3394", "strategy": "高频周期"}, {"structure": "6869", "strategy": "均值回归"}, {"structure": "0891", "strategy": "极值"}, {"structure": "7133", "strategy": "多样化处理"}, {"structure": "1090", "strategy": "极值"}, {"structure": "0088", "strategy": "极值"}, {"structure": "0561", "strategy": "多样化处理"}, {"structure": "0575", "strategy": "多样化处理"}, {"structure": "3101", "strategy": "高频周期"}, {"structure": "0303", "strategy": "高频周期"}, {"structure": "4668", "strategy": "均值回归"}, {"structure": "5167", "strategy": "多样化处理"}, {"structure": "7636", "strategy": "均值回归"}, {"structure": "3465", "strategy": "均值回归"}, {"structure": "9421", "strategy": "多样化处理"}, {"structure": "0923", "strategy": "高频周期"}, {"structure": "3163", "strategy": "高频周期"}, {"structure": "5764", "strategy": "均值回归"}, {"structure": "3414", "strategy": "高频周期"}, {"structure": "7201", "strategy": "多样化处理"}, {"structure": "8411", "strategy": "极值"}, {"structure": "4286", "strategy": "多样化处理"}, {"structure": "8003", "strategy": "极值"}, {"structure": "2450", "strategy": "高频周期"}, {"structure": "3344", "strategy": "高频周期"}, {"structure": "9170", "strategy": "极值"}, {"structure": "0820", "strategy": "极值"}, {"structure": "5744", "strategy": "均值回归"}, {"structure": "5673", "strategy": "高频周期"}, {"structure": "9137", "strategy": "高频周期"}, {"structure": "1101", "strategy": "极值"}, {"structure": "5322", "strategy": "多样化处理"}, {"structure": "5018", "strategy": "极值"}, {"structure": "7640", "strategy": "均值回归"}, {"structure": "5291", "strategy": "多样化处理"}, {"structure": "9810", "strategy": "极值"}, {"structure": "9607", "strategy": "均值回归"}, {"structure": "1334", "strategy": "高频周期"}, {"structure": "8191", "strategy": "极值"}, {"structure": "4465", "strategy": "高频周期"}, {"structure": "6577", "strategy": "多样化处理"}, {"structure": "9900", "strategy": "极值"}, {"structure": "6264", "strategy": "均值回归"}, {"structure": "0696", "strategy": "多样化处理"}, {"structure": "2680", "strategy": "多样化处理"}, {"structure": "6560", "strategy": "均值回归"}, {"structure": "6564", "strategy": "均值回归"}, {"structure": "4040", "strategy": "高频周期"}, {"structure": "1801", "strategy": "高频周期"}, {"structure": "0008", "strategy": "极值"}, {"structure": "6263", "strategy": "多样化处理"}, {"structure": "7246", "strategy": "多样化处理"}, {"structure": "8088", "strategy": "极值"}, {"structure": "7281", "strategy": "多样化处理"}, {"structure": "4282", "strategy": "多样化处理"}, {"structure": "5687", "strategy": "均值回归"}, {"structure": "5685", "strategy": "均值回归"}, {"structure": "7439", "strategy": "高频周期"}, {"structure": "7664", "strategy": "均值回归"}, {"structure": "1910", "strategy": "极值"}, {"structure": "6686", "strategy": "均值回归"}, {"structure": "1433", "strategy": "高频周期"}, {"structure": "0089", "strategy": "极值"}, {"structure": "9343", "strategy": "多样化处理"}, {"structure": "5182", "strategy": "极值"}, {"structure": "5469", "strategy": "均值回归"}], "strategy_statistics": {"均值回归": 25, "高频周期": 25, "极值": 25, "多样化处理": 25}, "metadata": {"generation_method": "strategy_based_with_tracking", "description": "原始候选结构（含策略追踪） - 第151期预测", "format": "4位数字字符串列表，包含策略来源信息"}}