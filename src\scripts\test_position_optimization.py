#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
位置优化测试脚本

本脚本用于验证多位置预测器的优化方案有效性，包括：
1. 位置自适应权重调整机制
2. 偏差校正算法
3. 位置相关性约束机制

测试内容：
- 对比优化前后的预测准确率
- 分析偏差校正效果
- 验证位置相关性约束的合理性
- 统计部分命中率的改善情况
"""

import os
import sys
import numpy as np
from typing import List
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))

from src.left_brain.multi_position_predictor import (
    MultiPositionPredictor,
)
from src.utils.logger import get_logger

# 配置日志
logger = get_logger("position_optimization_test")


def create_test_structures() -> List[str]:
    """创建测试结构列表"""
    # 包含用户观察到的部分命中案例
    test_structures = [
        "5646",
        "8064",
        "8285",
        "6966",  # 用户提供的部分命中案例
        "5648",
        "8068",
        "8085",
        "6766",  # 对应的实际中奖号码
        "1234",
        "5678",
        "9012",
        "3456",  # 连续数字测试
        "1111",
        "2222",
        "3333",
        "4444",  # 重复数字测试
        "1221",
        "3443",
        "5665",
        "7887",  # 对称数字测试
        "0123",
        "9876",
        "4567",
        "2109",  # 随机组合测试
    ]

    # 添加更多随机结构
    np.random.seed(42)
    for _ in range(50):
        structure = "".join([str(np.random.randint(0, 10)) for _ in range(4)])
        if structure not in test_structures:
            test_structures.append(structure)

    return test_structures


def create_test_features(structures: List[str]) -> np.ndarray:
    """为测试结构创建模拟特征矩阵"""
    n_structures = len(structures)
    n_features = 50  # 假设50个特征

    # 创建随机特征矩阵
    np.random.seed(42)
    feature_matrix = np.random.rand(n_structures, n_features).astype(np.float32)

    # 为特定结构添加一些模式
    for i, structure in enumerate(structures):
        digits = [int(d) for d in structure]

        # 基于数字特征调整特征值
        feature_matrix[i, 0] = sum(digits) / 36.0  # 数字和特征
        feature_matrix[i, 1] = len(set(digits)) / 4.0  # 唯一数字数量
        feature_matrix[i, 2] = max(digits) / 9.0  # 最大数字
        feature_matrix[i, 3] = min(digits) / 9.0  # 最小数字

        # 添加一些噪声
        feature_matrix[i, 4:] += np.random.normal(0, 0.1, n_features - 4)

    return feature_matrix


def test_basic_functionality():
    """测试基本功能"""
    logger.info("=" * 60)
    logger.info("测试1: 基本功能测试")
    logger.info("=" * 60)

    # 创建测试数据
    structures = create_test_structures()
    feature_matrix = create_test_features(structures)

    logger.info(f"测试结构数量: {len(structures)}")
    logger.info(f"特征矩阵形状: {feature_matrix.shape}")

    # 测试不同配置的预测器
    configs = [
        {
            "name": "默认配置",
            "weight_type": "default",
            "enable_bias_correction": False,
            "enable_adaptive_weights": False,
        },
        {
            "name": "优化权重",
            "weight_type": "optimized",
            "enable_bias_correction": False,
            "enable_adaptive_weights": False,
        },
        {
            "name": "偏差校正",
            "weight_type": "optimized",
            "enable_bias_correction": True,
            "enable_adaptive_weights": False,
        },
        {
            "name": "自适应权重",
            "weight_type": "optimized",
            "enable_bias_correction": False,
            "enable_adaptive_weights": True,
        },
        {
            "name": "完整优化",
            "weight_type": "optimized",
            "enable_bias_correction": True,
            "enable_adaptive_weights": True,
        },
    ]

    results = {}

    for config in configs:
        logger.info(f"\n测试配置: {config['name']}")

        try:
            # 创建预测器
            predictor = MultiPositionPredictor(
                scoring_strategy="weighted_average",
                weight_type=config["weight_type"],
                enable_bias_correction=config["enable_bias_correction"],
                enable_adaptive_weights=config["enable_adaptive_weights"],
            )

            # 模拟位置预测
            position_predictions = np.random.rand(len(structures), 4).astype(np.float32)

            # 计算评分
            scores = predictor.calculate_structure_scores(
                position_predictions,
                use_adaptive_weights=config["enable_adaptive_weights"],
                structures=structures,
            )

            # 分析结果
            results[config["name"]] = {
                "mean_score": np.mean(scores),
                "std_score": np.std(scores),
                "min_score": np.min(scores),
                "max_score": np.max(scores),
                "score_range": np.max(scores) - np.min(scores),
            }

            logger.info(f"  平均评分: {results[config['name']]['mean_score']:.6f}")
            logger.info(f"  评分标准差: {results[config['name']]['std_score']:.6f}")
            logger.info(
                f"  评分范围: [{results[config['name']]['min_score']:.6f}, {results[config['name']]['max_score']:.6f}]"
            )

        except Exception as e:
            logger.error(f"配置 {config['name']} 测试失败: {e}")
            results[config["name"]] = None

    return results


def test_bias_correction():
    """测试偏差校正功能"""
    logger.info("=" * 60)
    logger.info("测试2: 偏差校正功能测试")
    logger.info("=" * 60)

    # 创建预测器
    predictor = MultiPositionPredictor(
        weight_type="optimized",
        enable_bias_correction=True,
        enable_adaptive_weights=False,
    )

    # 模拟历史偏差数据（基于用户观察）
    predicted_structures = ["5646", "8064", "8285", "6966"]
    actual_structures = ["5648", "8068", "8085", "6766"]

    logger.info("模拟偏差校正历史数据:")
    logger.info(f"预测结构: {predicted_structures}")
    logger.info(f"实际结构: {actual_structures}")

    # 更新偏差校正历史
    predictor.update_bias_correction(predicted_structures, actual_structures)

    # 分析偏差模式
    if predictor.bias_correction_history:
        latest_bias = predictor.bias_correction_history[-1]
        logger.info("检测到的偏差模式:")
        for position, bias in latest_bias.items():
            logger.info(f"  {position}: {bias:+.2f}")

    # 测试偏差校正效果
    test_structures = ["5640", "8060", "8280", "6960"]  # 类似的结构
    create_test_features(test_structures)
    position_predictions = np.random.rand(len(test_structures), 4).astype(np.float32)

    # 计算校正前后的评分
    scores_before = predictor.calculate_structure_scores(
        position_predictions, use_adaptive_weights=False, structures=test_structures
    )

    # 临时禁用偏差校正
    predictor.enable_bias_correction = False
    scores_after = predictor.calculate_structure_scores(
        position_predictions, use_adaptive_weights=False, structures=test_structures
    )
    predictor.enable_bias_correction = True

    logger.info("\n偏差校正效果对比:")
    for i, structure in enumerate(test_structures):
        logger.info(
            f"  {structure}: 校正前={scores_before[i]:.6f}, 校正后={scores_after[i]:.6f}, 差异={scores_before[i]-scores_after[i]:+.6f}"
        )

    return {
        "bias_patterns": latest_bias if predictor.bias_correction_history else {},
        "correction_effects": list(scores_before - scores_after),
    }


def test_position_correlation():
    """测试位置相关性约束"""
    logger.info("=" * 60)
    logger.info("测试3: 位置相关性约束测试")
    logger.info("=" * 60)

    # 创建预测器
    predictor = MultiPositionPredictor(
        weight_type="optimized",
        enable_bias_correction=False,
        enable_adaptive_weights=False,
    )

    # 测试不同类型的结构
    test_cases = [
        {"structures": ["1234", "5678"], "type": "连续数字"},
        {"structures": ["1111", "2222"], "type": "重复数字"},
        {"structures": ["1221", "3443"], "type": "对称数字"},
        {"structures": ["1357", "2468"], "type": "正常组合"},
        {"structures": ["0123", "9876"], "type": "极值组合"},
    ]

    results = {}

    for test_case in test_cases:
        structures = test_case["structures"]
        case_type = test_case["type"]

        logger.info(f"\n测试 {case_type}: {structures}")

        # 创建特征矩阵
        create_test_features(structures)
        position_predictions = np.random.rand(len(structures), 4).astype(np.float32)

        # 计算约束前后的评分
        predictor.enable_position_correlation = False
        scores_before = predictor.calculate_structure_scores(
            position_predictions, use_adaptive_weights=False, structures=structures
        )

        predictor.enable_position_correlation = True
        scores_after = predictor.calculate_structure_scores(
            position_predictions, use_adaptive_weights=False, structures=structures
        )

        # 分析约束效果
        effects = scores_after - scores_before
        results[case_type] = {
            "structures": structures,
            "effects": effects.tolist(),
            "avg_effect": np.mean(effects),
        }

        logger.info("  约束效果:")
        for i, structure in enumerate(structures):
            effect_type = (
                "奖励" if effects[i] > 0 else "惩罚" if effects[i] < 0 else "无变化"
            )
            logger.info(f"    {structure}: {effects[i]:+.6f} ({effect_type})")

    return results


def main():
    """主测试函数"""
    logger.info("🚀 开始位置优化测试")
    logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # 测试1: 基本功能
        basic_results = test_basic_functionality()

        # 测试2: 偏差校正
        bias_results = test_bias_correction()

        # 测试3: 位置相关性约束
        correlation_results = test_position_correlation()

        # 汇总测试结果
        logger.info("=" * 60)
        logger.info("测试结果汇总")
        logger.info("=" * 60)

        logger.info("\n1. 基本功能测试结果:")
        for config_name, result in basic_results.items():
            if result:
                logger.info(
                    f"  {config_name}: 平均评分={result['mean_score']:.6f}, 评分范围={result['score_range']:.6f}"
                )

        logger.info("\n2. 偏差校正测试结果:")
        if bias_results["bias_patterns"]:
            logger.info("  检测到的偏差模式:")
            for position, bias in bias_results["bias_patterns"].items():
                logger.info(f"    {position}: {bias:+.2f}")

        logger.info("\n3. 位置相关性约束测试结果:")
        for case_type, result in correlation_results.items():
            logger.info(f"  {case_type}: 平均效果={result['avg_effect']:+.6f}")

        logger.info("\n✅ 所有测试完成")

        return True

    except Exception as e:
        logger.error(f"❌ 测试过程发生错误: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
