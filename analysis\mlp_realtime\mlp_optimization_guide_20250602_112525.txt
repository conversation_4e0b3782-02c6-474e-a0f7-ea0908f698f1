🎯 MLP优化指南报告
================================================================================
生成时间: 2025-06-02 11:25:25

📊 性能摘要
----------------------------------------
平均命中率: 8.33%
平均偏离度: 0.425
性能趋势: stable
最新等级: 良好

🚨 优先级行动
----------------------------------------
• 📈 中优先级：MLP性能有待提升
• 🔧 优化特征工程
• ⚖️ 调整融合权重

🤖 模型优化行动
----------------------------------------
• ✅ MLP预测方向正确

🔧 特征工程行动
----------------------------------------
• 📊 特征表现稳定，可考虑增加新特征

🎓 训练相关行动
----------------------------------------
• 🔄 建议重新训练MLP模型
• 📊 使用最近50期数据进行训练
• ⚙️ 调整训练参数：epochs=150, learning_rate=0.001
• 🎯 设置target_hit_rate=0.435（43.5%）
• 📈 使用动态训练保存期号特定模型

📊 监控行动
----------------------------------------
• 📊 继续监控每期MLP性能
• ⚠️ 设置命中率低于5%的告警
• 📈 跟踪偏离度评分变化
• 🔍 定期检查位置偏差分析

💻 具体执行命令
----------------------------------------
# 重新训练MLP模型
python src/left_brain/mlp_trainer.py --epochs 150 --learning_rate 0.001

# 验证新模型性能
python test_mlp_model_performance.py

# 调整融合权重（降低MLP权重）
# 在config.yaml中设置: rule=0.3, xgb=0.5, mlp=0.2

# 测试新权重效果
python test_fusion_weights.py

# 运行完整预测流程测试
python src/main.py --interactive

# 查看最新分析报告
python -c "from src.analyzer.mlp_performance_tracker import get_recent_performance_summary; print(get_recent_performance_summary())"