{"round": 151, "timestamp": "2025-06-06T23:03:32.916019", "count": 100, "expected_count": 100, "candidates": ["2877", "5776", "5467", "7246", "5777", "7707", "5673", "4021", "4537", "1898", "8911", "0971", "9919", "4040", "5469", "9799", "7921", "2783", "6640", "7206", "7283", "0820", "5457", "6657", "0867", "8189", "3314", "7842", "1090", "9666", "8915", "3447", "3843", "0088", "1111", "5556", "4865", "6673", "6563", "8981", "2949", "5560", "1780", "3455", "5018", "0089", "2663", "8110", "5422", "9170", "2337", "0303", "4050", "1264", "1000", "3163", "5765", "9939", "0532", "5291", "5294", "0696", "7709", "0998", "4565", "8217", "0118", "5167", "1910", "1334", "5657", "2233", "7133", "5565", "9421", "8411", "5686", "3568", "6263", "9343", "5357", "6850", "9015", "1409", "3101", "7281", "5685", "6262", "6869", "4452", "6662", "0407", "6915", "4304", "2460", "9607", "4744", "7774", "9137", "5129"], "candidates_with_strategy": [{"structure": "2877", "strategy": "多样化处理"}, {"structure": "5776", "strategy": "多样化处理"}, {"structure": "5467", "strategy": "多样化处理"}, {"structure": "7246", "strategy": "多样化处理"}, {"structure": "5777", "strategy": "均值回归"}, {"structure": "7707", "strategy": "多样化处理"}, {"structure": "5673", "strategy": "高频周期"}, {"structure": "4021", "strategy": "高频周期"}, {"structure": "4537", "strategy": "均值回归"}, {"structure": "1898", "strategy": "极值"}, {"structure": "8911", "strategy": "极值"}, {"structure": "0971", "strategy": "极值"}, {"structure": "9919", "strategy": "极值"}, {"structure": "4040", "strategy": "高频周期"}, {"structure": "5469", "strategy": "均值回归"}, {"structure": "9799", "strategy": "极值"}, {"structure": "7921", "strategy": "多样化处理"}, {"structure": "2783", "strategy": "高频周期"}, {"structure": "6640", "strategy": "均值回归"}, {"structure": "7206", "strategy": "多样化处理"}, {"structure": "7283", "strategy": "多样化处理"}, {"structure": "0820", "strategy": "极值"}, {"structure": "5457", "strategy": "均值回归"}, {"structure": "6657", "strategy": "均值回归"}, {"structure": "0867", "strategy": "多样化处理"}, {"structure": "8189", "strategy": "极值"}, {"structure": "3314", "strategy": "高频周期"}, {"structure": "7842", "strategy": "高频周期"}, {"structure": "1090", "strategy": "极值"}, {"structure": "9666", "strategy": "均值回归"}, {"structure": "8915", "strategy": "极值"}, {"structure": "3447", "strategy": "多样化处理"}, {"structure": "3843", "strategy": "高频周期"}, {"structure": "0088", "strategy": "极值"}, {"structure": "1111", "strategy": "极值"}, {"structure": "5556", "strategy": "均值回归"}, {"structure": "4865", "strategy": "均值回归"}, {"structure": "6673", "strategy": "均值回归"}, {"structure": "6563", "strategy": "均值回归"}, {"structure": "8981", "strategy": "极值"}, {"structure": "2949", "strategy": "高频周期"}, {"structure": "5560", "strategy": "高频周期"}, {"structure": "1780", "strategy": "极值"}, {"structure": "3455", "strategy": "高频周期"}, {"structure": "5018", "strategy": "极值"}, {"structure": "0089", "strategy": "极值"}, {"structure": "2663", "strategy": "高频周期"}, {"structure": "8110", "strategy": "极值"}, {"structure": "5422", "strategy": "多样化处理"}, {"structure": "9170", "strategy": "极值"}, {"structure": "2337", "strategy": "高频周期"}, {"structure": "0303", "strategy": "高频周期"}, {"structure": "4050", "strategy": "均值回归"}, {"structure": "1264", "strategy": "高频周期"}, {"structure": "1000", "strategy": "极值"}, {"structure": "3163", "strategy": "高频周期"}, {"structure": "5765", "strategy": "均值回归"}, {"structure": "9939", "strategy": "多样化处理"}, {"structure": "0532", "strategy": "高频周期"}, {"structure": "5291", "strategy": "多样化处理"}, {"structure": "5294", "strategy": "高频周期"}, {"structure": "0696", "strategy": "多样化处理"}, {"structure": "7709", "strategy": "多样化处理"}, {"structure": "0998", "strategy": "极值"}, {"structure": "4565", "strategy": "均值回归"}, {"structure": "8217", "strategy": "多样化处理"}, {"structure": "0118", "strategy": "极值"}, {"structure": "5167", "strategy": "多样化处理"}, {"structure": "1910", "strategy": "极值"}, {"structure": "1334", "strategy": "高频周期"}, {"structure": "5657", "strategy": "均值回归"}, {"structure": "2233", "strategy": "高频周期"}, {"structure": "7133", "strategy": "多样化处理"}, {"structure": "5565", "strategy": "均值回归"}, {"structure": "9421", "strategy": "多样化处理"}, {"structure": "8411", "strategy": "极值"}, {"structure": "5686", "strategy": "均值回归"}, {"structure": "3568", "strategy": "均值回归"}, {"structure": "6263", "strategy": "多样化处理"}, {"structure": "9343", "strategy": "多样化处理"}, {"structure": "5357", "strategy": "均值回归"}, {"structure": "6850", "strategy": "极值"}, {"structure": "9015", "strategy": "极值"}, {"structure": "1409", "strategy": "多样化处理"}, {"structure": "3101", "strategy": "高频周期"}, {"structure": "7281", "strategy": "多样化处理"}, {"structure": "5685", "strategy": "均值回归"}, {"structure": "6262", "strategy": "多样化处理"}, {"structure": "6869", "strategy": "均值回归"}, {"structure": "4452", "strategy": "高频周期"}, {"structure": "6662", "strategy": "均值回归"}, {"structure": "0407", "strategy": "高频周期"}, {"structure": "6915", "strategy": "均值回归"}, {"structure": "4304", "strategy": "高频周期"}, {"structure": "2460", "strategy": "高频周期"}, {"structure": "9607", "strategy": "均值回归"}, {"structure": "4744", "strategy": "均值回归"}, {"structure": "7774", "strategy": "多样化处理"}, {"structure": "9137", "strategy": "高频周期"}, {"structure": "5129", "strategy": "极值"}], "strategy_statistics": {"多样化处理": 25, "均值回归": 25, "高频周期": 25, "极值": 25}, "metadata": {"generation_method": "strategy_based_with_tracking", "description": "原始候选结构（含策略追踪） - 第151期预测", "format": "4位数字字符串列表，包含策略来源信息"}}