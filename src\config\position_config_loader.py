#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
位置优化配置加载器

本模块负责加载和管理多位置预测器的配置参数，包括：
1. 位置权重配置
2. 自适应权重调整参数
3. 偏差校正配置
4. 性能监控设置
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

# 获取日志器
logger = logging.getLogger(__name__)


@dataclass
class PositionWeightsConfig:
    """位置权重配置"""

    千位: float = 0.25
    百位: float = 0.25
    十位: float = 0.25
    个位: float = 0.25

    def to_dict(self) -> Dict[str, float]:
        """转换为字典格式"""
        return {
            "千位": self.千位,
            "百位": self.百位,
            "十位": self.十位,
            "个位": self.个位,
        }

    def normalize(self) -> "PositionWeightsConfig":
        """归一化权重"""
        total = self.千位 + self.百位 + self.十位 + self.个位
        if total == 0:
            return PositionWeightsConfig()  # 返回默认均等权重

        return PositionWeightsConfig(
            千位=self.千位 / total,
            百位=self.百位 / total,
            十位=self.十位 / total,
            个位=self.个位 / total,
        )


@dataclass
class AdaptiveWeightsConfig:
    """自适应权重配置"""

    enabled: bool = True
    accuracy_window: int = 30
    temperature: float = 2.0
    min_weight: float = 0.15
    max_weight: float = 0.40
    update_threshold: float = 0.05


@dataclass
class BiasCorrection:
    """偏差校正配置"""

    enabled: bool = True
    correction_window: int = 20
    max_correction_factor: float = 0.1
    position_bias_patterns: Dict[str, Dict[str, float]] = None

    def __post_init__(self):
        if self.position_bias_patterns is None:
            self.position_bias_patterns = {
                "千位": {"typical_bias": 0.0, "correction_strength": 0.5},
                "百位": {"typical_bias": 0.0, "correction_strength": 0.8},
                "十位": {"typical_bias": 0.0, "correction_strength": 0.5},
                "个位": {"typical_bias": 2.5, "correction_strength": 0.9},
            }


@dataclass
class PositionCorrelationConfig:
    """位置相关性约束配置"""

    enabled: bool = True
    correlation_strength: float = 0.15
    correlation_rules: Dict[str, Any] = None

    def __post_init__(self):
        if self.correlation_rules is None:
            self.correlation_rules = {
                "consecutive_penalty": 0.2,
                "repeat_penalty": 0.3,
                "symmetry_bonus": 0.1,
                "sum_constraints": {"min_sum": 10, "max_sum": 30, "penalty": 0.25},
                "position_diff_constraints": {"max_adjacent_diff": 7, "penalty": 0.15},
                "parity_balance": {"enabled": True, "all_same_parity_penalty": 0.2},
            }


@dataclass
class PositionOptimizationConfig:
    """位置优化完整配置"""

    position_weights: Dict[str, PositionWeightsConfig]
    adaptive_weights: AdaptiveWeightsConfig
    bias_correction: BiasCorrection
    position_correlation: PositionCorrelationConfig
    scoring_strategy: str = "weighted_average"
    performance_monitoring: Dict[str, Any] = None

    def __post_init__(self):
        if self.performance_monitoring is None:
            self.performance_monitoring = {
                "enabled": True,
                "log_level": "INFO",
                "detailed_analysis": False,
            }


class PositionConfigLoader:
    """位置优化配置加载器"""

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置加载器

        参数:
            config_path: 配置文件路径，如果为None则使用默认路径
        """
        if config_path is None:
            # 默认配置文件路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(current_dir, "position_optimization_config.yaml")

        self.config_path = config_path
        self._config_cache: Optional[PositionOptimizationConfig] = None

        logger.info(f"位置配置加载器初始化，配置文件: {config_path}")

    def load_config(self, force_reload: bool = False) -> PositionOptimizationConfig:
        """
        加载配置文件

        参数:
            force_reload: 是否强制重新加载

        返回:
            PositionOptimizationConfig: 配置对象
        """
        if self._config_cache is not None and not force_reload:
            return self._config_cache

        try:
            if not os.path.exists(self.config_path):
                logger.warning(f"配置文件不存在: {self.config_path}，使用默认配置")
                return self._create_default_config()

            with open(self.config_path, "r", encoding="utf-8") as f:
                config_data = yaml.safe_load(f)

            # 解析位置权重配置
            position_weights = {}
            weights_data = config_data.get("position_weights", {})

            for weight_type, weights in weights_data.items():
                position_weights[weight_type] = PositionWeightsConfig(
                    千位=weights.get("千位", 0.25),
                    百位=weights.get("百位", 0.25),
                    十位=weights.get("十位", 0.25),
                    个位=weights.get("个位", 0.25),
                ).normalize()

            # 解析自适应权重配置
            adaptive_data = config_data.get("adaptive_weights", {})
            adaptive_weights = AdaptiveWeightsConfig(
                enabled=adaptive_data.get("enabled", True),
                accuracy_window=adaptive_data.get("accuracy_window", 30),
                temperature=adaptive_data.get("temperature", 2.0),
                min_weight=adaptive_data.get("min_weight", 0.15),
                max_weight=adaptive_data.get("max_weight", 0.40),
                update_threshold=adaptive_data.get("update_threshold", 0.05),
            )

            # 解析偏差校正配置
            bias_data = config_data.get("bias_correction", {})
            bias_correction = BiasCorrection(
                enabled=bias_data.get("enabled", True),
                correction_window=bias_data.get("correction_window", 20),
                max_correction_factor=bias_data.get("max_correction_factor", 0.1),
                position_bias_patterns=bias_data.get("position_bias_patterns"),
            )

            # 解析位置相关性配置
            correlation_data = config_data.get("position_correlation", {})
            position_correlation = PositionCorrelationConfig(
                enabled=correlation_data.get("enabled", True),
                correlation_strength=correlation_data.get("correlation_strength", 0.15),
                correlation_rules=correlation_data.get("correlation_rules"),
            )

            # 创建完整配置
            config = PositionOptimizationConfig(
                position_weights=position_weights,
                adaptive_weights=adaptive_weights,
                bias_correction=bias_correction,
                position_correlation=position_correlation,
                scoring_strategy=config_data.get("scoring_strategies", {}).get(
                    "default", "weighted_average"
                ),
                performance_monitoring=config_data.get("performance_monitoring", {}),
            )

            self._config_cache = config
            logger.info("位置优化配置加载成功")
            logger.debug(f"加载的权重配置类型: {list(position_weights.keys())}")

            return config

        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            logger.info("使用默认配置")
            return self._create_default_config()

    def _create_default_config(self) -> PositionOptimizationConfig:
        """创建默认配置"""
        default_weights = PositionWeightsConfig().normalize()

        return PositionOptimizationConfig(
            position_weights={"default": default_weights},
            adaptive_weights=AdaptiveWeightsConfig(),
            bias_correction=BiasCorrection(),
            position_correlation=PositionCorrelationConfig(),
            scoring_strategy="weighted_average",
        )

    def get_position_weights(self, weight_type: str = "default") -> Dict[str, float]:
        """
        获取指定类型的位置权重

        参数:
            weight_type: 权重类型（default, optimized, conservative等）

        返回:
            Dict[str, float]: 位置权重字典
        """
        config = self.load_config()

        if weight_type in config.position_weights:
            return config.position_weights[weight_type].to_dict()
        else:
            logger.warning(f"权重类型 '{weight_type}' 不存在，使用默认权重")
            return config.position_weights["default"].to_dict()

    def get_adaptive_config(self) -> AdaptiveWeightsConfig:
        """获取自适应权重配置"""
        config = self.load_config()
        return config.adaptive_weights

    def get_bias_correction_config(self) -> BiasCorrection:
        """获取偏差校正配置"""
        config = self.load_config()
        return config.bias_correction

    def get_position_correlation_config(self) -> PositionCorrelationConfig:
        """获取位置相关性配置"""
        config = self.load_config()
        return config.position_correlation


# 全局配置加载器实例
_global_config_loader: Optional[PositionConfigLoader] = None


def get_position_config_loader() -> PositionConfigLoader:
    """获取全局配置加载器实例"""
    global _global_config_loader
    if _global_config_loader is None:
        _global_config_loader = PositionConfigLoader()
    return _global_config_loader


def load_position_weights(weight_type: str = "optimized") -> Dict[str, float]:
    """
    便捷函数：加载位置权重

    参数:
        weight_type: 权重类型

    返回:
        Dict[str, float]: 位置权重字典
    """
    loader = get_position_config_loader()
    return loader.get_position_weights(weight_type)


if __name__ == "__main__":
    # 测试配置加载器
    loader = PositionConfigLoader()
    config = loader.load_config()

    print("位置权重配置:")
    for weight_type, weights in config.position_weights.items():
        print(f"  {weight_type}: {weights.to_dict()}")

    print(f"\n自适应权重: {config.adaptive_weights}")
    print(f"偏差校正: {config.bias_correction}")
