#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多位置模型训练脚本

本脚本用于训练多位置预测模型，集成位置优化功能：
1. 位置自适应权重调整机制
2. 偏差校正算法
3. 位置相关性约束机制

训练完成后，模型将自动被系统识别并在预测时使用。
"""

import os
import sys
import json
import numpy as np
import polars as pl
from datetime import datetime
from typing import List, Tuple

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))

from src.left_brain.multi_position_trainer import MultiPositionTrainer
from src.left_brain.multi_position_predictor import MultiPositionPredictor
from src.data.data_loader import load_lottery_data
from src.data.feature_generator import generate_unified_features_polars
from src.utils.logger import get_logger

# 配置日志
logger = get_logger("multi_position_trainer")


def prepare_training_data(
    period_range: Tuple[int, int] = (1, 340)
) -> <PERSON><PERSON>[pl.DataFrame, pl.DataFrame]:
    """
    准备训练数据

    参数:
        period_range: 期数范围 (开始期, 结束期)

    返回:
        Tuple[pl.DataFrame, pl.DataFrame]: (特征数据, 标签数据)
    """
    logger.info(f"准备训练数据，期数范围: {period_range[0]}-{period_range[1]}")

    # 加载历史数据
    lottery_data = load_lottery_data()

    # 筛选期数范围
    training_data = lottery_data.filter(
        (pl.col("期数") >= period_range[0]) & (pl.col("期数") <= period_range[1])
    )

    logger.info(f"加载历史数据: {training_data.height} 期")

    # 生成特征数据
    logger.info("开始生成特征数据...")
    feature_data = generate_unified_features_polars(training_data)

    logger.info(f"特征数据生成完成: {feature_data.height} 个样本")

    # 生成标签数据（位置级标签）
    logger.info("开始生成位置级标签...")
    label_data = _generate_position_labels(training_data, feature_data)

    logger.info(f"标签数据生成完成: {label_data.height} 个样本")

    return feature_data, label_data


def _generate_position_labels(
    lottery_data: pl.DataFrame, feature_data: pl.DataFrame
) -> pl.DataFrame:
    """
    生成位置级标签数据

    参数:
        lottery_data: 彩票历史数据
        feature_data: 特征数据

    返回:
        pl.DataFrame: 位置级标签数据
    """
    # 提取所有中奖号码
    winning_numbers = set()
    for row in lottery_data.iter_rows(named=True):
        winning_numbers.add(row["中奖号码"])

    logger.info(f"历史中奖号码数量: {len(winning_numbers)}")

    # 为每个结构生成位置级标签
    label_records = []

    for row in feature_data.iter_rows(named=True):
        structure = row["structure"]

        # 检查每个位置是否在历史中奖号码中出现过
        position_labels = {"千位": 0, "百位": 0, "十位": 0, "个位": 0}

        if len(structure) == 4:
            for winning_num in winning_numbers:
                if len(winning_num) == 4:
                    # 检查各位置匹配
                    if structure[0] == winning_num[0]:  # 千位
                        position_labels["千位"] = 1
                    if structure[1] == winning_num[1]:  # 百位
                        position_labels["百位"] = 1
                    if structure[2] == winning_num[2]:  # 十位
                        position_labels["十位"] = 1
                    if structure[3] == winning_num[3]:  # 个位
                        position_labels["个位"] = 1

        # 整体命中标签
        overall_hit = 1 if structure in winning_numbers else 0

        label_records.append(
            {
                "structure": structure,
                "千位_label": position_labels["千位"],
                "百位_label": position_labels["百位"],
                "十位_label": position_labels["十位"],
                "个位_label": position_labels["个位"],
                "overall_hit": overall_hit,
            }
        )

    return pl.DataFrame(label_records)


def train_multi_position_model(
    feature_data: pl.DataFrame,
    label_data: pl.DataFrame,
    model_save_path: str = "models/multi_position",
) -> bool:
    """
    训练多位置模型

    参数:
        feature_data: 特征数据
        label_data: 标签数据
        model_save_path: 模型保存路径

    返回:
        bool: 训练是否成功
    """
    logger.info("开始训练多位置模型...")

    try:
        # 创建训练器
        trainer = MultiPositionTrainer()

        # 训练模型
        success = trainer.train_models(feature_data, label_data, model_save_path)

        if success:
            # 创建多位置模型标识文件
            metadata_file = os.path.join(
                model_save_path, "multi_position_metadata.json"
            )
            metadata = {
                "model_type": "multi_position",
                "version": "1.0.0",
                "created_date": datetime.now().isoformat(),
                "training_samples": feature_data.height,
                "features": {
                    "position_weights": "optimized",
                    "bias_correction": True,
                    "adaptive_weights": True,
                    "position_correlation": True,
                },
                "description": "多位置预测模型，支持位置优化功能",
            }

            with open(metadata_file, "w", encoding="utf-8") as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)

            logger.info(f"✅ 多位置模型训练成功，保存到: {model_save_path}")
            logger.info(f"📄 模型元数据已保存: {metadata_file}")

            return True
        else:
            logger.error("❌ 多位置模型训练失败")
            return False

    except Exception as e:
        logger.error(f"❌ 多位置模型训练过程发生错误: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_trained_model(model_path: str, test_structures: List[str] = None) -> bool:
    """
    测试训练好的模型

    参数:
        model_path: 模型路径
        test_structures: 测试结构列表

    返回:
        bool: 测试是否成功
    """
    logger.info("开始测试训练好的多位置模型...")

    if test_structures is None:
        test_structures = ["5646", "8064", "8285", "6966", "1234", "5678"]

    try:
        # 创建预测器
        predictor = MultiPositionPredictor(
            scoring_strategy="weighted_average",
            weight_type="optimized",
            enable_bias_correction=True,
            enable_adaptive_weights=True,
        )

        # 加载模型
        if not predictor.load_models(model_path):
            logger.error("模型加载失败")
            return False

        # 创建测试特征
        test_features = np.random.rand(len(test_structures), 50).astype(np.float32)

        # 进行预测
        scores = predictor.predict_structure_scores(test_structures, test_features)

        logger.info("测试预测结果:")
        for structure, score in scores.items():
            logger.info(f"  {structure}: {score:.6f}")

        logger.info("✅ 模型测试成功")
        return True

    except Exception as e:
        logger.error(f"❌ 模型测试失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("🚀 开始多位置模型训练流程")
    logger.info(f"训练时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # 1. 准备训练数据
        logger.info("=" * 60)
        logger.info("步骤1: 准备训练数据")
        logger.info("=" * 60)

        feature_data, label_data = prepare_training_data(period_range=(1, 340))

        # 2. 训练模型
        logger.info("=" * 60)
        logger.info("步骤2: 训练多位置模型")
        logger.info("=" * 60)

        model_path = "models/multi_position"
        success = train_multi_position_model(feature_data, label_data, model_path)

        if not success:
            logger.error("❌ 训练失败，退出程序")
            return False

        # 3. 测试模型
        logger.info("=" * 60)
        logger.info("步骤3: 测试训练好的模型")
        logger.info("=" * 60)

        test_success = test_trained_model(model_path)

        if test_success:
            logger.info("=" * 60)
            logger.info("🎉 多位置模型训练流程完成")
            logger.info("=" * 60)
            logger.info("✅ 模型已准备就绪，系统将自动使用多位置预测器")
            logger.info(f"📁 模型路径: {os.path.abspath(model_path)}")
            return True
        else:
            logger.error("❌ 模型测试失败")
            return False

    except Exception as e:
        logger.error(f"❌ 训练流程发生错误: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
