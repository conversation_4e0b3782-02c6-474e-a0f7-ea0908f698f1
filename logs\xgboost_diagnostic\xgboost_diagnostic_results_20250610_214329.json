{"optimization_info": {"timestamp": "20250610_214329", "n_trials": 10, "duration_seconds": 6475.157221794128, "target_period": 151, "train_period_range": [1, 150]}, "best_result": {"trial_number": 0, "params": {"max_depth": 6, "learning_rate": 0.28570714885887566, "n_estimators": 2330, "subsample": 0.8394633936788146, "colsample_bytree": 0.6624074561769746, "min_child_weight": 1.6443457513284063, "gamma": 0.05808361216819946, "reg_alpha": 0.8661761457749352, "reg_lambda": 0.6011150117432088, "scale_pos_weight": 708.3645052182494}, "objective_value": 0.0, "success": false}, "statistics": {"total_trials": 10, "successful_trials": 0, "success_rate": 0.0, "best_hit_count": 0}, "optimization_history": [{"trial_number": 0, "params": {"max_depth": 6, "learning_rate": 0.28570714885887566, "n_estimators": 2330, "subsample": 0.8394633936788146, "colsample_bytree": 0.6624074561769746, "min_child_weight": 1.6443457513284063, "gamma": 0.05808361216819946, "reg_alpha": 0.8661761457749352, "reg_lambda": 0.6011150117432088, "scale_pos_weight": 708.3645052182494}, "performance": {"hit_count": 0, "success": 0, "top30": [], "hit_structures": []}, "objective_value": 0}, {"trial_number": 1, "params": {"max_depth": 3, "learning_rate": 0.29127385712697834, "n_estimators": 2581, "subsample": 0.6849356442713105, "colsample_bytree": 0.6727299868828402, "min_child_weight": 1.915704647548995, "gamma": 0.3042422429595377, "reg_alpha": 0.5247564316322378, "reg_lambda": 0.43194501864211576, "scale_pos_weight": 291.93791105784385}, "performance": {"hit_count": 0, "success": 0, "top30": [], "hit_structures": []}, "objective_value": 0}, {"trial_number": 2, "params": {"max_depth": 9, "learning_rate": 0.05045321958909213, "n_estimators": 1230, "subsample": 0.7465447373174767, "colsample_bytree": 0.7824279936868144, "min_child_weight": 7.873242017790835, "gamma": 0.19967378215835974, "reg_alpha": 0.5142344384136116, "reg_lambda": 0.5924145688620425, "scale_pos_weight": 47.40396230727773}, "performance": {"hit_count": 0, "success": 0, "top30": [], "hit_structures": []}, "objective_value": 0}, {"trial_number": 3, "params": {"max_depth": 9, "learning_rate": 0.059451995869314544, "n_estimators": 662, "subsample": 0.9795542149013333, "colsample_bytree": 0.9862528132298237, "min_child_weight": 8.103133746352965, "gamma": 0.3046137691733707, "reg_alpha": 0.09767211400638387, "reg_lambda": 0.6842330265121569, "scale_pos_weight": 440.7123412458617}, "performance": {"hit_count": 0, "success": 0, "top30": [], "hit_structures": []}, "objective_value": 0}, {"trial_number": 4, "params": {"max_depth": 4, "learning_rate": 0.15360130393226834, "n_estimators": 586, "subsample": 0.9637281608315128, "colsample_bytree": 0.7035119926400067, "min_child_weight": 6.658970615104422, "gamma": 0.31171107608941095, "reg_alpha": 0.5200680211778108, "reg_lambda": 0.5467102793432796, "scale_pos_weight": 185.6696010700015}, "performance": {"hit_count": 0, "success": 0, "top30": [], "hit_structures": []}, "objective_value": 0}, {"trial_number": 5, "params": {"max_depth": 12, "learning_rate": 0.2347885187747232, "n_estimators": 2849, "subsample": 0.9579309401710595, "colsample_bytree": 0.8391599915244341, "min_child_weight": 9.226554926728857, "gamma": 0.0884925020519195, "reg_alpha": 0.1959828624191452, "reg_lambda": 0.045227288910538066, "scale_pos_weight": 326.00500043250105}, "performance": {"hit_count": 0, "success": 0, "top30": [], "hit_structures": []}, "objective_value": 0}, {"trial_number": 6, "params": {"max_depth": 6, "learning_rate": 0.08869121921442981, "n_estimators": 2572, "subsample": 0.7427013306774357, "colsample_bytree": 0.7123738038749523, "min_child_weight": 5.47269122326666, "gamma": 0.14092422497476265, "reg_alpha": 0.8021969807540397, "reg_lambda": 0.07455064367977082, "scale_pos_weight": 986.9000496639168}, "performance": {"hit_count": 0, "success": 0, "top30": [], "hit_structures": []}, "objective_value": 0}, {"trial_number": 7, "params": {"max_depth": 10, "learning_rate": 0.06762754764491, "n_estimators": 513, "subsample": 0.9261845713819337, "colsample_bytree": 0.8827429375390468, "min_child_weight": 7.317170963605775, "gamma": 0.7712703466859457, "reg_alpha": 0.07404465173409036, "reg_lambda": 0.3584657285442726, "scale_pos_weight": 116.75319046560459}, "performance": {"hit_count": 0, "success": 0, "top30": [], "hit_structures": []}, "objective_value": 0}, {"trial_number": 8, "params": {"max_depth": 11, "learning_rate": 0.19075645677999178, "n_estimators": 1327, "subsample": 0.6254233401144095, "colsample_bytree": 0.7243929286862649, "min_child_weight": 3.319314888064796, "gamma": 0.7296061783380641, "reg_alpha": 0.6375574713552131, "reg_lambda": 0.8872127425763265, "scale_pos_weight": 472.74271023678733}, "performance": {"hit_count": 0, "success": 0, "top30": [], "hit_structures": []}, "objective_value": 0}, {"trial_number": 9, "params": {"max_depth": 4, "learning_rate": 0.21684098829466855, "n_estimators": 2402, "subsample": 0.8245108790277985, "colsample_bytree": 0.9083868719818244, "min_child_weight": 4.9885764040074685, "gamma": 0.5227328293819941, "reg_alpha": 0.42754101835854963, "reg_lambda": 0.02541912674409519, "scale_pos_weight": 108.78353556631114}, "performance": {"hit_count": 0, "success": 0, "top30": [], "hit_structures": []}, "objective_value": 0}]}