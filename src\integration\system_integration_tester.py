#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
系统集成测试器 - 第五阶段系统集成测试
验证所有优化组件的集成效果和整体性能
"""

import os
import sys
import time
import traceback
import numpy as np
import polars as pl
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

try:
    from src.utils.logger import get_logger
    from src.data.feature_generator import Feature
    from src.data.number_splitter import SplitRecord
    logger = get_logger(__name__)
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)


@dataclass
class IntegrationTestResult:
    """集成测试结果"""
    component_name: str
    test_passed: bool
    execution_time: float
    error_message: Optional[str] = None
    performance_metrics: Optional[Dict[str, float]] = None


@dataclass
class SystemPerformanceReport:
    """系统性能报告"""
    total_tests: int
    passed_tests: int
    failed_tests: int
    total_execution_time: float
    component_results: List[IntegrationTestResult]
    overall_score: float


class SystemIntegrationTester:
    """系统集成测试器"""
    
    def __init__(self):
        self.test_results = []
        self.test_data = self._create_test_data()
        
    def _create_test_data(self) -> Tuple[List[SplitRecord], pl.DataFrame, Feature]:
        """创建测试数据"""
        # 创建历史数据
        test_records = []
        for i in range(1, 51):  # 50期历史数据
            record = SplitRecord(
                round=i,
                thousands=np.random.randint(0, 10),
                hundreds=np.random.randint(0, 10),
                tens=np.random.randint(0, 10),
                units=np.random.randint(0, 10)
            )
            test_records.append(record)
        
        # 创建测试DataFrame
        test_df = pl.DataFrame({
            "期号": [r.round for r in test_records],
            "千位": [r.thousands for r in test_records],
            "百位": [r.hundreds for r in test_records],
            "十位": [r.tens for r in test_records],
            "个位": [r.units for r in test_records]
        })
        
        # 创建测试特征
        test_features = Feature(features={
            'freq5_k_1': 0.2, 'freq5_k_2': 0.3,
            'mean_k': 4.5, 'std_k': 2.8,
            'consecutive_k': 1.0, 'missing_k_5': 3.0
        })
        
        return test_records, test_df, test_features
    
    def test_optimized_feature_generator(self) -> IntegrationTestResult:
        """测试优化特征生成器"""
        logger.info("🧪 测试优化特征生成器")
        start_time = time.time()
        
        try:
            from src.data.optimized_feature_generator import generate_optimized_features
            
            test_records, test_df, _ = self.test_data
            
            # 生成优化特征
            features_df = generate_optimized_features(test_df, 51, ["1234", "5678"])
            
            # 验证结果
            assert not features_df.is_empty(), "特征DataFrame不应为空"
            assert 'structure' in features_df.columns, "应包含structure列"
            assert 'features' in features_df.columns, "应包含features列"
            
            # 验证特征数量
            sample_features = features_df[0, 'features']
            assert len(sample_features) > 10, f"特征数量过少: {len(sample_features)}"
            
            execution_time = time.time() - start_time
            
            return IntegrationTestResult(
                component_name="优化特征生成器",
                test_passed=True,
                execution_time=execution_time,
                performance_metrics={
                    'feature_count': len(sample_features),
                    'structures_processed': features_df.height
                }
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ 优化特征生成器测试失败: {e}")
            return IntegrationTestResult(
                component_name="优化特征生成器",
                test_passed=False,
                execution_time=execution_time,
                error_message=str(e)
            )
    
    def test_enhanced_candidate_generator(self) -> IntegrationTestResult:
        """测试增强候选生成器"""
        logger.info("🧪 测试增强候选生成器")
        start_time = time.time()
        
        try:
            from src.right_brain.enhanced_candidate_generator import EnhancedCandidateGenerator
            
            test_records, _, test_features = self.test_data
            
            # 创建生成器
            generator = EnhancedCandidateGenerator()
            
            # 生成候选
            candidates = generator.generate_comprehensive_candidates(
                test_records, 51, test_features, 1000
            )
            
            # 验证结果
            assert len(candidates) > 0, "候选数量不应为0"
            assert len(candidates) <= 1000, f"候选数量超出预期: {len(candidates)}"
            assert all(len(c) == 4 for c in candidates), "所有候选应为4位数字"
            
            # 验证唯一性
            unique_candidates = len(set(candidates))
            uniqueness_ratio = unique_candidates / len(candidates)
            
            execution_time = time.time() - start_time
            
            return IntegrationTestResult(
                component_name="增强候选生成器",
                test_passed=True,
                execution_time=execution_time,
                performance_metrics={
                    'candidate_count': len(candidates),
                    'unique_candidates': unique_candidates,
                    'uniqueness_ratio': uniqueness_ratio
                }
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ 增强候选生成器测试失败: {e}")
            return IntegrationTestResult(
                component_name="增强候选生成器",
                test_passed=False,
                execution_time=execution_time,
                error_message=str(e)
            )
    
    def test_enhanced_scoring_system(self) -> IntegrationTestResult:
        """测试增强评分系统"""
        logger.info("🧪 测试增强评分系统")
        start_time = time.time()
        
        try:
            from src.left_brain.enhanced_scoring_system import enhanced_score_structures
            
            # 创建测试数据
            test_structures = ["1234", "5678", "9012", "3456", "7890"]
            test_rule_scores = {s: np.random.random() for s in test_structures}
            test_model_scores = {s: np.random.random() for s in test_structures}
            test_mlp_scores = {s: np.random.random() for s in test_structures}
            test_features = {
                'freq5_k_1': 0.2, 'freq5_k_2': 0.3,
                'mean_k': 4.5, 'std_k': 2.8,
                'consecutive_k': 1.0, 'missing_k_5': 3.0
            }
            
            # 测试增强评分
            enhanced_scores = enhanced_score_structures(
                test_structures, test_rule_scores, test_model_scores,
                test_mlp_scores, test_features, use_enhanced=True
            )
            
            # 验证结果
            assert len(enhanced_scores) == len(test_structures), "评分结果数量不匹配"
            assert all(0 <= score <= 1 for score in enhanced_scores.values()), "评分应在[0,1]范围内"
            
            # 计算评分质量指标
            scores = list(enhanced_scores.values())
            score_variance = np.var(scores)
            score_mean = np.mean(scores)
            
            execution_time = time.time() - start_time
            
            return IntegrationTestResult(
                component_name="增强评分系统",
                test_passed=True,
                execution_time=execution_time,
                performance_metrics={
                    'structures_scored': len(enhanced_scores),
                    'score_mean': score_mean,
                    'score_variance': score_variance
                }
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ 增强评分系统测试失败: {e}")
            return IntegrationTestResult(
                component_name="增强评分系统",
                test_passed=False,
                execution_time=execution_time,
                error_message=str(e)
            )
    
    def test_config_consistency(self) -> IntegrationTestResult:
        """测试配置一致性"""
        logger.info("🧪 测试配置一致性")
        start_time = time.time()
        
        try:
            from src.config.config_validator import ConfigValidator
            
            # 创建验证器
            validator = ConfigValidator()
            
            # 验证配置
            issues = validator.validate_all_configs()
            
            # 检查是否有问题
            has_issues = bool(issues)
            
            execution_time = time.time() - start_time
            
            return IntegrationTestResult(
                component_name="配置一致性",
                test_passed=not has_issues,
                execution_time=execution_time,
                error_message=str(issues) if has_issues else None,
                performance_metrics={
                    'config_files_checked': 3,
                    'issues_found': len(issues)
                }
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ 配置一致性测试失败: {e}")
            return IntegrationTestResult(
                component_name="配置一致性",
                test_passed=False,
                execution_time=execution_time,
                error_message=str(e)
            )
    
    def test_end_to_end_pipeline(self) -> IntegrationTestResult:
        """测试端到端流水线"""
        logger.info("🧪 测试端到端流水线")
        start_time = time.time()
        
        try:
            # 1. 特征生成
            from src.data.feature_integration import generate_features_unified
            test_records, test_df, _ = self.test_data
            features_df = generate_features_unified(test_df, 51, ["1234", "5678"], use_optimized=True)
            
            # 2. 候选生成
            from src.right_brain.enhanced_candidate_generator import integrate_enhanced_candidates
            test_features = Feature(features={'test': 1.0})
            candidates, coverage = integrate_enhanced_candidates(test_records, 51, test_features, 100)
            
            # 3. 评分融合
            from src.left_brain.enhanced_scoring_system import enhanced_score_structures
            test_rule_scores = {c: np.random.random() for c in candidates[:10]}
            test_model_scores = {c: np.random.random() for c in candidates[:10]}
            final_scores = enhanced_score_structures(
                candidates[:10], test_rule_scores, test_model_scores, use_enhanced=True
            )
            
            # 验证流水线完整性
            assert not features_df.is_empty(), "特征生成失败"
            assert len(candidates) > 0, "候选生成失败"
            assert len(final_scores) > 0, "评分融合失败"
            
            execution_time = time.time() - start_time
            
            return IntegrationTestResult(
                component_name="端到端流水线",
                test_passed=True,
                execution_time=execution_time,
                performance_metrics={
                    'features_generated': features_df.height,
                    'candidates_generated': len(candidates),
                    'structures_scored': len(final_scores),
                    'coverage_rate': coverage.get('coverage_rate', 0.0)
                }
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ 端到端流水线测试失败: {e}")
            logger.error(f"详细错误: {traceback.format_exc()}")
            return IntegrationTestResult(
                component_name="端到端流水线",
                test_passed=False,
                execution_time=execution_time,
                error_message=str(e)
            )
    
    def run_all_tests(self) -> SystemPerformanceReport:
        """运行所有集成测试"""
        logger.info("🚀 开始系统集成测试")
        
        test_methods = [
            self.test_optimized_feature_generator,
            self.test_enhanced_candidate_generator,
            self.test_enhanced_scoring_system,
            self.test_config_consistency,
            self.test_end_to_end_pipeline,
        ]
        
        results = []
        total_start_time = time.time()
        
        for test_method in test_methods:
            try:
                result = test_method()
                results.append(result)
                
                if result.test_passed:
                    logger.info(f"✅ {result.component_name} 测试通过 ({result.execution_time:.2f}s)")
                else:
                    logger.error(f"❌ {result.component_name} 测试失败: {result.error_message}")
                    
            except Exception as e:
                logger.error(f"❌ 测试方法 {test_method.__name__} 执行失败: {e}")
                results.append(IntegrationTestResult(
                    component_name=test_method.__name__,
                    test_passed=False,
                    execution_time=0.0,
                    error_message=str(e)
                ))
        
        total_execution_time = time.time() - total_start_time
        
        # 计算统计信息
        passed_tests = sum(1 for r in results if r.test_passed)
        failed_tests = len(results) - passed_tests
        overall_score = passed_tests / len(results) if results else 0.0
        
        report = SystemPerformanceReport(
            total_tests=len(results),
            passed_tests=passed_tests,
            failed_tests=failed_tests,
            total_execution_time=total_execution_time,
            component_results=results,
            overall_score=overall_score
        )
        
        self._generate_test_report(report)
        return report
    
    def _generate_test_report(self, report: SystemPerformanceReport) -> None:
        """生成测试报告"""
        logger.info("📊 生成系统集成测试报告")
        
        report_content = f"""
🎯 系统集成测试报告
{'='*60}
测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
总测试数: {report.total_tests}
通过测试: {report.passed_tests}
失败测试: {report.failed_tests}
总执行时间: {report.total_execution_time:.2f}秒
整体评分: {report.overall_score:.2%}

📋 详细测试结果:
{'-'*60}
"""
        
        for result in report.component_results:
            status = "✅ 通过" if result.test_passed else "❌ 失败"
            report_content += f"""
组件: {result.component_name}
状态: {status}
执行时间: {result.execution_time:.2f}秒
"""
            if result.error_message:
                report_content += f"错误信息: {result.error_message}\n"
            
            if result.performance_metrics:
                report_content += f"性能指标: {result.performance_metrics}\n"
        
        # 保存报告
        try:
            os.makedirs("logs/integration_tests", exist_ok=True)
            report_file = f"logs/integration_tests/integration_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            logger.info(f"📄 测试报告已保存: {report_file}")
        except Exception as e:
            logger.error(f"❌ 保存测试报告失败: {e}")
        
        # 输出到日志
        logger.info(report_content)


if __name__ == "__main__":
    # 运行系统集成测试
    logger.info("🎯 启动系统集成测试")
    
    tester = SystemIntegrationTester()
    report = tester.run_all_tests()
    
    if report.overall_score >= 0.8:
        logger.info(f"🎉 系统集成测试成功完成！整体评分: {report.overall_score:.2%}")
    else:
        logger.warning(f"⚠️ 系统集成测试存在问题，整体评分: {report.overall_score:.2%}")
    
    logger.info("✅ 系统集成测试完成")
