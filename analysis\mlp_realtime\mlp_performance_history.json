[{"period": 341, "timestamp": "2025-06-02T11:22:02.015405", "top30_structures": ["1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029"], "top30_scores": {"1000": 0.8, "1001": 0.79, "1002": 0.78, "1003": 0.77, "1004": 0.76, "1005": 0.75, "1006": 0.74, "1007": 0.73, "1008": 0.7200000000000001, "1009": 0.7100000000000001, "1010": 0.7000000000000001, "1011": 0.6900000000000001, "1012": 0.68, "1013": 0.67, "1014": 0.66, "1015": 0.65, "1016": 0.64, "1017": 0.63, "1018": 0.6200000000000001, "1019": 0.6100000000000001, "1020": 0.6000000000000001, "1021": 0.5900000000000001, "1022": 0.5800000000000001, "1023": 0.5700000000000001, "1024": 0.56, "1025": 0.55, "1026": 0.54, "1027": 0.53, "1028": 0.52, "1029": 0.51}, "mlp_evaluation_complete": true, "real_numbers": ["1005", "1015", "2000", "3000", "4000", "5000", "5001", "5002", "5003", "5004", "5005", "5006", "5007", "5008", "5009", "5010", "5011", "5012", "5013", "5014", "5015", "5016", "5017"], "analysis_complete": true, "real_numbers_timestamp": "2025-06-02T11:22:03.025608", "hit_count": 2, "hit_rate": 0.06666666666666667, "exact_hits": ["1005", "1015"], "distance_analysis": {"avg_distance": 2.2333333333333334, "min_distance": 0, "max_distance": 4, "distance_distribution": {"1": 6, "2": 9, "3": 9, "0": 2, "4": 4}}, "position_bias": [0.3, 0.0, 0.5333333333333333, 1.4], "mlp_drift_score": 0.44033333333333335, "performance_grade": "一般"}, {"period": 342, "timestamp": "2025-06-02T11:22:04.037609", "top30_structures": ["0000", "0001", "0002", "0003", "0004", "0005", "0006", "0007", "0008", "0009", "0010", "0011", "0012", "0013", "0014", "0015", "0016", "0017", "0018", "0019", "0020", "0021", "0022", "0023", "0024", "0025", "0026", "0027", "0028", "0029"], "top30_scores": {"0000": 0.9, "0001": 0.892, "0002": 0.884, "0003": 0.876, "0004": 0.868, "0005": 0.86, "0006": 0.852, "0007": 0.844, "0008": 0.8360000000000001, "0009": 0.8280000000000001, "0010": 0.8200000000000001, "0011": 0.812, "0012": 0.804, "0013": 0.796, "0014": 0.788, "0015": 0.78, "0016": 0.772, "0017": 0.764, "0018": 0.756, "0019": 0.748, "0020": 0.74, "0021": 0.732, "0022": 0.724, "0023": 0.716, "0024": 0.708, "0025": 0.7, "0026": 0.692, "0027": 0.684, "0028": 0.676, "0029": 0.668}, "mlp_evaluation_complete": true, "real_numbers": ["0002", "0005", "0010", "8000", "8001", "8002", "8003", "8004", "8005", "8006", "8007", "8008", "8009", "8010", "8011", "8012", "8013", "8014", "8015", "8016", "8017", "8018", "8019"], "analysis_complete": true, "real_numbers_timestamp": "2025-06-02T11:22:04.037609", "hit_count": 3, "hit_rate": 0.1, "exact_hits": ["0002", "0005", "0010"], "distance_analysis": {"avg_distance": 1.6333333333333333, "min_distance": 0, "max_distance": 3, "distance_distribution": {"1": 10, "0": 3, "2": 12, "3": 5}}, "position_bias": [0.2, 0.0, 0.7333333333333333, 0.7], "mlp_drift_score": 0.40900000000000003, "performance_grade": "良好"}, {"period": 341, "timestamp": "2025-06-02T11:32:49.495667", "top30_structures": ["1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029"], "top30_scores": {"1000": 0.8, "1001": 0.79, "1002": 0.78, "1003": 0.77, "1004": 0.76, "1005": 0.75, "1006": 0.74, "1007": 0.73, "1008": 0.7200000000000001, "1009": 0.7100000000000001, "1010": 0.7000000000000001, "1011": 0.6900000000000001, "1012": 0.68, "1013": 0.67, "1014": 0.66, "1015": 0.65, "1016": 0.64, "1017": 0.63, "1018": 0.6200000000000001, "1019": 0.6100000000000001, "1020": 0.6000000000000001, "1021": 0.5900000000000001, "1022": 0.5800000000000001, "1023": 0.5700000000000001, "1024": 0.56, "1025": 0.55, "1026": 0.54, "1027": 0.53, "1028": 0.52, "1029": 0.51}, "mlp_evaluation_complete": true, "real_numbers": ["1005", "1015", "2000", "3000", "4000", "5000", "5001", "5002", "5003", "5004", "5005", "5006", "5007", "5008", "5009", "5010", "5011", "5012", "5013", "5014", "5015", "5016", "5017"], "analysis_complete": true, "real_numbers_timestamp": "2025-06-02T11:32:50.508691", "hit_count": 2, "hit_rate": 0.06666666666666667, "exact_hits": ["1005", "1015"], "distance_analysis": {"avg_distance": 2.2333333333333334, "min_distance": 0, "max_distance": 4, "distance_distribution": {"1": 6, "2": 9, "3": 9, "0": 2, "4": 4}}, "position_bias": [0.3, 0.0, 0.5333333333333333, 1.4], "mlp_drift_score": 0.44033333333333335, "performance_grade": "一般"}, {"period": 342, "timestamp": "2025-06-02T11:32:51.517540", "top30_structures": ["0000", "0001", "0002", "0003", "0004", "0005", "0006", "0007", "0008", "0009", "0010", "0011", "0012", "0013", "0014", "0015", "0016", "0017", "0018", "0019", "0020", "0021", "0022", "0023", "0024", "0025", "0026", "0027", "0028", "0029"], "top30_scores": {"0000": 0.9, "0001": 0.892, "0002": 0.884, "0003": 0.876, "0004": 0.868, "0005": 0.86, "0006": 0.852, "0007": 0.844, "0008": 0.8360000000000001, "0009": 0.8280000000000001, "0010": 0.8200000000000001, "0011": 0.812, "0012": 0.804, "0013": 0.796, "0014": 0.788, "0015": 0.78, "0016": 0.772, "0017": 0.764, "0018": 0.756, "0019": 0.748, "0020": 0.74, "0021": 0.732, "0022": 0.724, "0023": 0.716, "0024": 0.708, "0025": 0.7, "0026": 0.692, "0027": 0.684, "0028": 0.676, "0029": 0.668}, "mlp_evaluation_complete": true, "real_numbers": ["0002", "0005", "0010", "8000", "8001", "8002", "8003", "8004", "8005", "8006", "8007", "8008", "8009", "8010", "8011", "8012", "8013", "8014", "8015", "8016", "8017", "8018", "8019"], "analysis_complete": true, "real_numbers_timestamp": "2025-06-02T11:32:51.524818", "hit_count": 3, "hit_rate": 0.1, "exact_hits": ["0002", "0005", "0010"], "distance_analysis": {"avg_distance": 1.6333333333333333, "min_distance": 0, "max_distance": 3, "distance_distribution": {"1": 10, "0": 3, "2": 12, "3": 5}}, "position_bias": [0.2, 0.0, 0.7333333333333333, 0.7], "mlp_drift_score": 0.40900000000000003, "performance_grade": "良好"}]