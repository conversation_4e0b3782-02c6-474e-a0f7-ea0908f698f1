{"optimization_info": {"timestamp": "20250611_153613", "n_trials": 1, "duration_seconds": 237.95416641235352, "target_period": 151, "train_period_range": [1, 150]}, "best_result": {"trial_number": 0, "params": {"max_depth": 6, "learning_rate": 0.28570714885887566, "n_estimators": 2330, "subsample": 0.8394633936788146, "colsample_bytree": 0.6624074561769746, "min_child_weight": 1.6443457513284063, "gamma": 0.05808361216819946, "reg_alpha": 0.8661761457749352, "reg_lambda": 0.6011150117432088, "scale_pos_weight": 72.26689489062431}, "objective_value": 0.0, "success": false}, "statistics": {"total_trials": 1, "successful_trials": 0, "success_rate": 0.0, "best_hit_count": 0}, "optimization_history": [{"trial_number": 0, "params": {"max_depth": 6, "learning_rate": 0.28570714885887566, "n_estimators": 2330, "subsample": 0.8394633936788146, "colsample_bytree": 0.6624074561769746, "min_child_weight": 1.6443457513284063, "gamma": 0.05808361216819946, "reg_alpha": 0.8661761457749352, "reg_lambda": 0.6011150117432088, "scale_pos_weight": 72.26689489062431}, "performance": {"hit_count": 0, "success": 0, "top30": ["2562", "1958", "2598", "5453", "2720", "6442", "7886", "0508", "6760", "9519", "3310", "3111", "6769", "8897", "1162", "7182", "0963", "5442", "7113", "0944", "2229", "1040", "6359", "4985", "4992", "7668", "0859", "0474", "9796", "9259"], "hit_structures": [], "target_winning_numbers": ["1100", "5869", "7715", "7819", "8256", "7635", "9457", "0831", "5762", "0201", "3744", "9436", "6832", "2462", "5674", "9240", "6893", "0696", "9054", "6959", "4611", "8660", "6173"], "top30_scores": [{"结构": "2562", "XGBoost分数": 0.9999916553497314}, {"结构": "1958", "XGBoost分数": 0.9999916553497314}, {"结构": "2598", "XGBoost分数": 0.9999916553497314}, {"结构": "5453", "XGBoost分数": 0.9999916553497314}, {"结构": "2720", "XGBoost分数": 0.9999916553497314}, {"结构": "6442", "XGBoost分数": 0.9999916553497314}, {"结构": "7886", "XGBoost分数": 0.9999916553497314}, {"结构": "0508", "XGBoost分数": 0.9999916553497314}, {"结构": "6760", "XGBoost分数": 0.9999916553497314}, {"结构": "9519", "XGBoost分数": 0.9999916553497314}, {"结构": "3310", "XGBoost分数": 0.9999916553497314}, {"结构": "3111", "XGBoost分数": 0.9999916553497314}, {"结构": "6769", "XGBoost分数": 0.9999916553497314}, {"结构": "8897", "XGBoost分数": 0.9999916553497314}, {"结构": "1162", "XGBoost分数": 0.9999916553497314}, {"结构": "7182", "XGBoost分数": 0.9999916553497314}, {"结构": "0963", "XGBoost分数": 0.9999916553497314}, {"结构": "5442", "XGBoost分数": 0.9999916553497314}, {"结构": "7113", "XGBoost分数": 0.9999916553497314}, {"结构": "0944", "XGBoost分数": 0.9999916553497314}, {"结构": "2229", "XGBoost分数": 0.9999916553497314}, {"结构": "1040", "XGBoost分数": 0.9999916553497314}, {"结构": "6359", "XGBoost分数": 0.9999916553497314}, {"结构": "4985", "XGBoost分数": 0.9999916553497314}, {"结构": "4992", "XGBoost分数": 0.9999916553497314}, {"结构": "7668", "XGBoost分数": 0.9999916553497314}, {"结构": "0859", "XGBoost分数": 0.9999916553497314}, {"结构": "0474", "XGBoost分数": 0.9999916553497314}, {"结构": "9796", "XGBoost分数": 0.9999916553497314}, {"结构": "9259", "XGBoost分数": 0.9999916553497314}]}, "objective_value": 0, "hit_count": 0}]}