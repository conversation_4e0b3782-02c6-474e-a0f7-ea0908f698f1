# 位置优化配置文件
# 用于配置多位置预测器的权重调整和偏差校正参数

# 位置权重配置
position_weights:
  # 默认均等权重
  default:
    千位: 0.25
    百位: 0.25
    十位: 0.25
    个位: 0.25
  
  # 基于历史表现的优化权重
  optimized:
    千位: 0.28  # 千位预测相对稳定，权重略高
    百位: 0.22  # 百位存在偏差，权重略低
    十位: 0.26  # 十位表现良好
    个位: 0.24  # 个位存在正偏差，权重略低
  
  # 保守权重配置（差异较小）
  conservative:
    千位: 0.26
    百位: 0.24
    十位: 0.26
    个位: 0.24

# 自适应权重调整配置
adaptive_weights:
  enabled: true
  accuracy_window: 30        # 准确率统计窗口大小
  temperature: 2.0           # softmax温度参数，控制权重分布的极端程度
  min_weight: 0.15          # 最小权重限制
  max_weight: 0.40          # 最大权重限制
  update_threshold: 0.05    # 权重更新阈值

# 偏差校正配置
bias_correction:
  enabled: true
  correction_window: 20      # 偏差校正滑动窗口大小
  max_correction_factor: 0.1 # 最大校正因子（10%）
  
  # 位置特定偏差模式（基于用户观察）
  position_bias_patterns:
    千位:
      typical_bias: 0.0      # 千位通常无明显偏差
      correction_strength: 0.5
    百位:
      typical_bias: 0.0      # 百位偏差双向，需要动态校正
      correction_strength: 0.8
    十位:
      typical_bias: 0.0      # 十位相对稳定
      correction_strength: 0.5
    个位:
      typical_bias: 2.5      # 个位存在正偏差(+2到+4)
      correction_strength: 0.9

# 评分策略配置
scoring_strategies:
  default: "weighted_average"
  available:
    - "weighted_average"     # 加权平均（推荐）
    - "product"             # 乘积（严格）
    - "minimum"             # 最小值（保守）
    - "harmonic_mean"       # 调和平均（平衡）

# 性能监控配置
performance_monitoring:
  enabled: true
  log_level: "INFO"
  detailed_analysis: false   # 是否输出详细的位置贡献分析
  
  # 命中率统计
  hit_rate_tracking:
    enabled: true
    track_partial_hits: true  # 跟踪部分命中（3位匹配等）
    
  # 偏差统计
  bias_tracking:
    enabled: true
    track_position_bias: true
    bias_alert_threshold: 3.0  # 偏差警告阈值

# 位置相关性约束配置
position_correlation:
  enabled: true              # 启用位置相关性约束
  correlation_strength: 0.15 # 相关性约束强度

  # 位置间相关性规则
  correlation_rules:
    # 连续数字惩罚（如1234, 5678等）
    consecutive_penalty: 0.2

    # 重复数字惩罚（如1111, 2222等）
    repeat_penalty: 0.3

    # 对称数字奖励（如1221, 3443等）
    symmetry_bonus: 0.1

    # 数字和范围约束
    sum_constraints:
      min_sum: 10    # 四位数字和最小值
      max_sum: 30    # 四位数字和最大值
      penalty: 0.25  # 超出范围的惩罚

    # 位置间差值约束
    position_diff_constraints:
      # 相邻位置差值不宜过大
      max_adjacent_diff: 7
      penalty: 0.15

    # 奇偶性平衡
    parity_balance:
      enabled: true
      # 全奇数或全偶数的惩罚
      all_same_parity_penalty: 0.2

# 实验性功能配置
experimental:
  # 动态权重学习
  dynamic_learning:
    enabled: false           # 暂时禁用，需要更多验证
    learning_rate: 0.01
    momentum: 0.9

  # 高级相关性分析
  advanced_correlation:
    enabled: false           # 暂时禁用
    use_historical_patterns: true
    pattern_window: 100

# 配置版本和元数据
metadata:
  version: "1.0.0"
  created_date: "2024-01-07"
  description: "多位置预测器优化配置，解决部分命中问题"
  author: "AI Assistant"
  
  # 配置变更历史
  changelog:
    - version: "1.0.0"
      date: "2024-01-07"
      changes:
        - "初始版本，添加位置权重和偏差校正配置"
        - "基于用户观察的偏差模式进行参数调优"
        - "支持自适应权重调整机制"
