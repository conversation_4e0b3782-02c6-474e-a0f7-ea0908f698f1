#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多位置模型集成测试脚本

本脚本用于测试多位置模型是否已正确集成到主系统中：
1. 测试模型检测逻辑
2. 测试预测流程集成
3. 验证优化功能是否生效
"""

import os
import sys
import numpy as np
import polars as pl

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))

from src.left_brain.model_scorer import (
    _is_multi_position_model,
    predict_model_scores_polars,
)
from src.brain.brain_core import BrainCore
from src.utils.logger import get_logger

# 配置日志
logger = get_logger("multi_position_integration_test")


def test_multi_position_detection():
    """测试多位置模型检测功能"""
    logger.info("=" * 60)
    logger.info("测试1: 多位置模型检测功能")
    logger.info("=" * 60)

    # 测试多位置模型路径
    multi_position_path = "models/multi_position"

    # 检测结果
    is_multi_position = _is_multi_position_model(multi_position_path)

    logger.info(f"多位置模型路径: {multi_position_path}")
    logger.info(
        f"检测结果: {'✅ 是多位置模型' if is_multi_position else '❌ 不是多位置模型'}"
    )

    # 检查元数据文件
    metadata_file = os.path.join(multi_position_path, "multi_position_metadata.json")
    metadata_exists = os.path.exists(metadata_file)

    logger.info(f"元数据文件: {metadata_file}")
    logger.info(f"元数据存在: {'✅ 存在' if metadata_exists else '❌ 不存在'}")

    if metadata_exists:
        import json

        try:
            with open(metadata_file, "r", encoding="utf-8") as f:
                metadata = json.load(f)
            logger.info(f"元数据内容: {metadata.get('model_type', 'N/A')}")
            logger.info(f"优化功能: {metadata.get('features', {})}")
        except Exception as e:
            logger.error(f"读取元数据失败: {e}")

    return is_multi_position and metadata_exists


def test_model_scorer_integration():
    """测试模型评分器集成"""
    logger.info("=" * 60)
    logger.info("测试2: 模型评分器集成")
    logger.info("=" * 60)

    # 创建测试数据
    test_structures = ["5646", "8064", "8285", "6966", "1234"]

    # 创建测试特征DataFrame
    feature_data = []
    for structure in test_structures:
        # 创建模拟特征
        features = {f"feature_{i}": np.random.rand() for i in range(20)}
        feature_data.append({"structure": structure, "features": features})

    feature_df = pl.DataFrame(feature_data)

    logger.info(f"测试结构: {test_structures}")
    logger.info(f"特征DataFrame形状: {feature_df.shape}")

    # 测试模型评分
    try:
        model_path = "models/multi_position"
        scores = predict_model_scores_polars(test_structures, feature_df, model_path)

        logger.info("评分结果:")
        for structure, score in scores.items():
            logger.info(f"  {structure}: {score:.6f}")

        logger.info("✅ 模型评分器集成测试成功")
        return True

    except Exception as e:
        logger.error(f"❌ 模型评分器集成测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_brain_core_integration():
    """测试BrainCore集成"""
    logger.info("=" * 60)
    logger.info("测试3: BrainCore集成")
    logger.info("=" * 60)

    try:
        # 创建BrainCore实例
        config = {
            "model_path": "models/multi_position",
            "max_candidates": 100,
            "scoring": {"force_three_models": False},
        }

        brain = BrainCore(config)

        # 测试模型选择逻辑
        selected_model = brain._select_model_for_prediction(341)

        logger.info(f"选择的模型路径: {selected_model}")

        # 检查是否选择了多位置模型
        if "multi_position" in selected_model:
            logger.info("✅ BrainCore正确选择了多位置模型")
            return True
        else:
            logger.warning(f"⚠️ BrainCore选择了其他模型: {selected_model}")
            return False

    except Exception as e:
        logger.error(f"❌ BrainCore集成测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_optimization_features():
    """测试优化功能"""
    logger.info("=" * 60)
    logger.info("测试4: 优化功能验证")
    logger.info("=" * 60)

    try:
        from src.left_brain.multi_position_predictor import MultiPositionPredictor
        from src.config.position_config_loader import get_position_config_loader

        # 测试配置加载器
        config_loader = get_position_config_loader()
        config = config_loader.load_config()

        logger.info("配置加载测试:")
        logger.info(f"  位置权重类型: {list(config.position_weights.keys())}")
        logger.info(
            f"  自适应权重: {'启用' if config.adaptive_weights.enabled else '禁用'}"
        )
        logger.info(
            f"  偏差校正: {'启用' if config.bias_correction.enabled else '禁用'}"
        )
        logger.info(
            f"  位置相关性: {'启用' if config.position_correlation.enabled else '禁用'}"
        )

        # 测试多位置预测器
        predictor = MultiPositionPredictor(
            scoring_strategy="weighted_average",
            weight_type="optimized",
            enable_bias_correction=True,
            enable_adaptive_weights=True,
        )

        logger.info("多位置预测器创建:")
        logger.info(f"  位置权重: {predictor.position_weights}")
        logger.info(
            f"  偏差校正: {'启用' if predictor.enable_bias_correction else '禁用'}"
        )
        logger.info(
            f"  自适应权重: {'启用' if predictor.enable_adaptive_weights else '禁用'}"
        )
        logger.info(
            f"  位置相关性: {'启用' if predictor.enable_position_correlation else '禁用'}"
        )

        logger.info("✅ 优化功能验证成功")
        return True

    except Exception as e:
        logger.error(f"❌ 优化功能验证失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    logger.info("🚀 开始多位置模型集成测试")
    logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    test_results = {}

    # 测试1: 多位置模型检测
    test_results["detection"] = test_multi_position_detection()

    # 测试2: 模型评分器集成
    test_results["scorer_integration"] = test_model_scorer_integration()

    # 测试3: BrainCore集成
    test_results["brain_integration"] = test_brain_core_integration()

    # 测试4: 优化功能
    test_results["optimization_features"] = test_optimization_features()

    # 汇总结果
    logger.info("=" * 60)
    logger.info("测试结果汇总")
    logger.info("=" * 60)

    all_passed = True
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if not result:
            all_passed = False

    if all_passed:
        logger.info("=" * 60)
        logger.info("🎉 所有测试通过！多位置模型已成功集成到系统中")
        logger.info("=" * 60)
        logger.info("系统现在将自动使用多位置预测器进行预测，包括：")
        logger.info("✅ 位置自适应权重调整")
        logger.info("✅ 偏差校正算法")
        logger.info("✅ 位置相关性约束")
        logger.info("✅ 针对'仅差一步完全命中4位'的优化")
    else:
        logger.error("=" * 60)
        logger.error("❌ 部分测试失败，请检查集成配置")
        logger.error("=" * 60)

    return all_passed


if __name__ == "__main__":
    from datetime import datetime

    success = main()
    sys.exit(0 if success else 1)
