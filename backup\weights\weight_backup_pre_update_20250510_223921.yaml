features:
  normalize_features: true
  use_cross_position: true
  use_markov: true
  window_10: 10
  window_30: 30
  window_5: 5
iteration:
  early_stop_on_hit: true
  feedback_mode: path_tagged
  feedback_trigger_on_zero: true
  max_rounds: 5
logging:
  level: INFO
  log_format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  log_to_file: true
model:
  path: models/xgb_model.json
  threshold: 0.5
  type: xgboost
  use_model: true
paths:
  data_path: data/lottery_history.xlsx
  feedback_save_path: data/feedback
  log_dir: logs
  memory_path: data/memory
  model_dir: models
  result_save_path: data/results
rule_weights:
  cold_b: 0.3
  cold_g: 0.3
  cold_k: 0.3
  cold_t: 0.3
  hot_b: -0.2
  hot_g: -0.2
  hot_k: -0.2
  hot_t: -0.2
scoring:
  model_weight: 0.6
  normalize_scores: true
  rule_weight: 0.4
  score_clip_range:
    max: 1.0
    min: 0.0
selection:
  diversity_enforce: true
  filter_illegal: true
  top_n: 20
strategy_paths:
  allow_random_disturbance: true
  max_active_paths: 2
  min_weight_threshold: 0.1
  冷修复: 0.2
  爆破: 0.3
  稳定结构: 0.25
  高频周期: 0.25
system:
  candidate_count: 1000
  retrain_interval: 5
