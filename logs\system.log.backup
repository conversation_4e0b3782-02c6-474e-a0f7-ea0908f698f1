[2025-06-29 21:44:33] [INFO] [prediction_system] [logger.py:122] [4648:MainThread] 日志系统初始化完成
[2025-06-29 21:44:33] [DEBUG] [prediction_system] [logger.py:123] [4648:MainThread] 日志级别设置为: DEBUG
[2025-06-29 21:44:34] [DEBUG] [tensorflow] [tpu_cluster_resolver.py:34] [4648:MainThread] Falling back to TensorFlow client; we recommended you install the Cloud TPU client directly with pip install cloud-tpu-client.
[2025-06-29 21:44:35] [DEBUG] [h5py._conv] [__init__.py:47] [4648:MainThread] Creating converter from 7 to 5
[2025-06-29 21:44:35] [DEBUG] [h5py._conv] [__init__.py:47] [4648:MainThread] Creating converter from 5 to 7
[2025-06-29 21:44:35] [DEBUG] [h5py._conv] [__init__.py:47] [4648:MainThread] Creating converter from 7 to 5
[2025-06-29 21:44:35] [DEBUG] [h5py._conv] [__init__.py:47] [4648:MainThread] Creating converter from 5 to 7
[2025-06-29 21:44:36] [INFO] [src.integrator] [integrator.py:94] [4648:MainThread] 系统集成器初始化完成（包含状态缓存机制）
[2025-06-29 21:44:36] [INFO] [src.data.data_loader] [data_loader.py:586] [4648:MainThread] 使用指定的数据路径: 历史数据.xlsx
[2025-06-29 21:44:36] [INFO] [src.data.data_loader] [data_loader.py:85] [4648:MainThread] 成功读取Excel文件: 历史数据.xlsx，共 351 行
[2025-06-29 21:44:36] [INFO] [src.data.data_loader] [data_loader.py:376] [4648:MainThread] 检测到宽格式数据，包含 23 个奖项列，转换为长格式
[2025-06-29 21:44:36] [INFO] [src.data.data_loader] [data_loader.py:431] [4648:MainThread] 成功转换为长格式，共 8073 行
[2025-06-29 21:44:36] [INFO] [src.data.data_loader] [data_loader.py:440] [4648:MainThread] 🎯 检测到实际期数范围: 1-351，共 351 期
[2025-06-29 21:44:36] [INFO] [src.data.data_loader] [data_loader.py:447] [4648:MainThread] ✅ 期数范围信息已保存，建议训练范围: 1-351
[2025-06-29 21:44:36] [INFO] [src.data.data_loader] [data_loader.py:510] [4648:MainThread] 成功标准化结构列，共 8073 行
[2025-06-29 21:44:36] [INFO] [src.data.data_loader] [data_loader.py:628] [4648:MainThread] 成功加载并标准化数据，共 8073 行
