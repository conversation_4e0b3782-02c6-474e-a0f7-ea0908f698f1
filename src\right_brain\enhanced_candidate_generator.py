#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强候选生成器 - 第三阶段候选生成策略扩展
提供全覆盖的候选生成策略，确保不遗漏任何可能的中奖组合
"""

import os
import sys
import random
import itertools
import numpy as np
from typing import Dict, List, Set, Optional, Tuple
from collections import defaultdict

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

try:
    from src.utils.logger import get_logger
    from src.data.feature_generator import Feature
    from src.data.number_splitter import SplitRecord
    logger = get_logger(__name__)
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)


class EnhancedCandidateGenerator:
    """增强候选生成器 - 全覆盖策略"""
    
    def __init__(self):
        self.strategies = {
            # 基础策略
            'frequency_based': self._generate_frequency_based,
            'pattern_based': self._generate_pattern_based,
            'statistical_based': self._generate_statistical_based,
            'trend_based': self._generate_trend_based,
            
            # 高级策略
            'combinatorial_coverage': self._generate_combinatorial_coverage,
            'gap_analysis': self._generate_gap_analysis,
            'correlation_based': self._generate_correlation_based,
            'ensemble_voting': self._generate_ensemble_voting,
            
            # 全覆盖策略
            'systematic_coverage': self._generate_systematic_coverage,
            'adaptive_sampling': self._generate_adaptive_sampling,
        }
        
        # 策略权重（基于预测效果）
        self.strategy_weights = {
            'frequency_based': 0.20,
            'pattern_based': 0.15,
            'statistical_based': 0.15,
            'trend_based': 0.10,
            'combinatorial_coverage': 0.15,
            'gap_analysis': 0.10,
            'correlation_based': 0.05,
            'ensemble_voting': 0.05,
            'systematic_coverage': 0.03,
            'adaptive_sampling': 0.02,
        }
    
    def generate_comprehensive_candidates(self, data: List[SplitRecord], 
                                        current_round: int, features: Feature,
                                        target_count: int = 10000) -> List[str]:
        """生成全面的候选结构"""
        logger.info(f"🚀 开始生成全面候选结构，目标数量: {target_count}")
        
        all_candidates = set()
        strategy_contributions = {}
        
        # 1. 使用所有策略生成候选
        for strategy_name, strategy_func in self.strategies.items():
            try:
                weight = self.strategy_weights.get(strategy_name, 0.1)
                strategy_count = int(target_count * weight)
                
                logger.info(f"📊 执行策略: {strategy_name}, 目标数量: {strategy_count}")
                strategy_candidates = strategy_func(data, current_round, features, strategy_count)
                
                # 记录策略贡献
                new_candidates = set(strategy_candidates) - all_candidates
                strategy_contributions[strategy_name] = len(new_candidates)
                
                all_candidates.update(strategy_candidates)
                logger.info(f"✅ {strategy_name} 贡献新候选: {len(new_candidates)}")
                
            except Exception as e:
                logger.error(f"❌ 策略 {strategy_name} 执行失败: {e}")
                continue
        
        # 2. 如果候选数量不足，使用补充策略
        if len(all_candidates) < target_count:
            needed = target_count - len(all_candidates)
            logger.info(f"⚠️ 候选数量不足，需要补充: {needed}")
            
            # 使用智能补充策略
            supplement_candidates = self._generate_intelligent_supplement(
                data, current_round, features, needed, list(all_candidates)
            )
            all_candidates.update(supplement_candidates)
        
        # 3. 转换为列表并随机打乱
        final_candidates = list(all_candidates)
        random.shuffle(final_candidates)
        
        # 4. 输出统计信息
        logger.info(f"🎯 候选生成完成:")
        logger.info(f"   总候选数量: {len(final_candidates)}")
        logger.info(f"   策略贡献: {strategy_contributions}")
        
        return final_candidates[:target_count]
    
    def _generate_frequency_based(self, data: List[SplitRecord], current_round: int, 
                                features: Feature, count: int) -> List[str]:
        """基于频率的候选生成"""
        candidates = []
        
        if not data:
            return self._generate_random_candidates(count)
        
        # 分析各位置数字频率
        position_frequencies = self._analyze_position_frequencies(data)
        
        # 生成基于频率的候选
        for _ in range(count):
            candidate = ""
            for pos in ['千位', '百位', '十位', '个位']:
                if pos in position_frequencies:
                    # 按频率权重选择数字
                    digits = list(position_frequencies[pos].keys())
                    weights = list(position_frequencies[pos].values())
                    if weights:
                        digit = random.choices(digits, weights=weights)[0]
                        candidate += str(digit)
                    else:
                        candidate += str(random.randint(0, 9))
                else:
                    candidate += str(random.randint(0, 9))
            
            if len(candidate) == 4:
                candidates.append(candidate)
        
        return candidates
    
    def _generate_pattern_based(self, data: List[SplitRecord], current_round: int,
                              features: Feature, count: int) -> List[str]:
        """基于模式的候选生成"""
        candidates = []
        
        if not data:
            return self._generate_random_candidates(count)
        
        # 分析历史模式
        patterns = self._extract_patterns(data)
        
        # 基于模式生成候选
        for _ in range(count):
            if patterns:
                # 选择一个模式作为基础
                base_pattern = random.choice(patterns)
                # 对模式进行变异
                candidate = self._mutate_pattern(base_pattern)
                candidates.append(candidate)
            else:
                candidates.append(self._generate_random_candidate())
        
        return candidates
    
    def _generate_statistical_based(self, data: List[SplitRecord], current_round: int,
                                  features: Feature, count: int) -> List[str]:
        """基于统计的候选生成"""
        candidates = []
        
        if not data:
            return self._generate_random_candidates(count)
        
        # 计算统计特征
        stats = self._calculate_position_statistics(data)
        
        # 基于统计分布生成候选
        for _ in range(count):
            candidate = ""
            for pos in ['千位', '百位', '十位', '个位']:
                if pos in stats:
                    mean = stats[pos]['mean']
                    std = stats[pos]['std']
                    # 使用正态分布生成数字
                    digit = int(np.clip(np.random.normal(mean, std), 0, 9))
                    candidate += str(digit)
                else:
                    candidate += str(random.randint(0, 9))
            
            candidates.append(candidate)
        
        return candidates
    
    def _generate_trend_based(self, data: List[SplitRecord], current_round: int,
                            features: Feature, count: int) -> List[str]:
        """基于趋势的候选生成"""
        candidates = []
        
        if len(data) < 5:
            return self._generate_random_candidates(count)
        
        # 分析趋势
        trends = self._analyze_trends(data[-10:])  # 使用最近10期数据
        
        # 基于趋势生成候选
        for _ in range(count):
            candidate = ""
            for pos in ['千位', '百位', '十位', '个位']:
                if pos in trends:
                    trend = trends[pos]
                    # 根据趋势预测下一个数字
                    if trend > 0:
                        # 上升趋势，选择较大的数字
                        digit = random.randint(5, 9)
                    elif trend < 0:
                        # 下降趋势，选择较小的数字
                        digit = random.randint(0, 4)
                    else:
                        # 平稳趋势，随机选择
                        digit = random.randint(0, 9)
                    candidate += str(digit)
                else:
                    candidate += str(random.randint(0, 9))
            
            candidates.append(candidate)
        
        return candidates
    
    def _generate_combinatorial_coverage(self, data: List[SplitRecord], current_round: int,
                                       features: Feature, count: int) -> List[str]:
        """组合覆盖策略 - 确保覆盖重要的数字组合"""
        candidates = []
        
        # 生成重要的数字组合
        important_combinations = self._get_important_combinations(data)
        
        # 为每个重要组合生成候选
        combinations_per_group = max(1, count // len(important_combinations)) if important_combinations else count
        
        for combo in important_combinations:
            for _ in range(combinations_per_group):
                candidate = self._build_candidate_from_combination(combo)
                candidates.append(candidate)
                if len(candidates) >= count:
                    break
            if len(candidates) >= count:
                break
        
        # 如果不足，随机补充
        while len(candidates) < count:
            candidates.append(self._generate_random_candidate())
        
        return candidates[:count]
    
    def _generate_gap_analysis(self, data: List[SplitRecord], current_round: int,
                             features: Feature, count: int) -> List[str]:
        """间隔分析策略 - 基于数字出现间隔"""
        candidates = []
        
        if not data:
            return self._generate_random_candidates(count)
        
        # 分析数字间隔
        gaps = self._analyze_digit_gaps(data)
        
        # 基于间隔生成候选
        for _ in range(count):
            candidate = ""
            for pos in ['千位', '百位', '十位', '个位']:
                if pos in gaps:
                    # 选择间隔较长的数字（冷号）
                    pos_gaps = gaps[pos]
                    if pos_gaps:
                        # 按间隔长度加权选择
                        digits = list(pos_gaps.keys())
                        weights = list(pos_gaps.values())
                        digit = random.choices(digits, weights=weights)[0]
                        candidate += str(digit)
                    else:
                        candidate += str(random.randint(0, 9))
                else:
                    candidate += str(random.randint(0, 9))
            
            candidates.append(candidate)
        
        return candidates
    
    def _generate_correlation_based(self, data: List[SplitRecord], current_round: int,
                                  features: Feature, count: int) -> List[str]:
        """基于相关性的候选生成"""
        candidates = []
        
        if len(data) < 10:
            return self._generate_random_candidates(count)
        
        # 分析位置间相关性
        correlations = self._analyze_position_correlations(data)
        
        # 基于相关性生成候选
        for _ in range(count):
            candidate = self._generate_correlated_candidate(correlations)
            candidates.append(candidate)
        
        return candidates
    
    def _generate_ensemble_voting(self, data: List[SplitRecord], current_round: int,
                                features: Feature, count: int) -> List[str]:
        """集成投票策略"""
        candidates = []
        
        # 使用多个子策略投票
        sub_strategies = ['frequency_based', 'pattern_based', 'statistical_based']
        votes_per_strategy = count // len(sub_strategies)
        
        for strategy_name in sub_strategies:
            if strategy_name in self.strategies:
                strategy_func = self.strategies[strategy_name]
                strategy_candidates = strategy_func(data, current_round, features, votes_per_strategy)
                candidates.extend(strategy_candidates)
        
        # 随机补充到目标数量
        while len(candidates) < count:
            candidates.append(self._generate_random_candidate())
        
        return candidates[:count]
    
    def _generate_systematic_coverage(self, data: List[SplitRecord], current_round: int,
                                    features: Feature, count: int) -> List[str]:
        """系统性覆盖策略 - 确保覆盖所有可能的数字组合"""
        candidates = []
        
        # 生成系统性覆盖的候选
        # 确保每个数字在每个位置都有代表
        positions = 4
        digits_per_position = min(count // (positions * 10), 1)
        
        for pos in range(positions):
            for digit in range(10):
                for _ in range(digits_per_position):
                    candidate = ""
                    for p in range(positions):
                        if p == pos:
                            candidate += str(digit)
                        else:
                            candidate += str(random.randint(0, 9))
                    candidates.append(candidate)
                    if len(candidates) >= count:
                        return candidates[:count]
        
        # 随机补充
        while len(candidates) < count:
            candidates.append(self._generate_random_candidate())
        
        return candidates[:count]
    
    def _generate_adaptive_sampling(self, data: List[SplitRecord], current_round: int,
                                  features: Feature, count: int) -> List[str]:
        """自适应采样策略"""
        candidates = []
        
        # 根据历史表现自适应调整采样策略
        if len(data) >= 20:
            # 分析最近的表现
            recent_data = data[-20:]
            performance_metrics = self._calculate_performance_metrics(recent_data)
            
            # 基于性能指标调整采样
            for _ in range(count):
                candidate = self._adaptive_sample_candidate(performance_metrics)
                candidates.append(candidate)
        else:
            candidates = self._generate_random_candidates(count)
        
        return candidates
    
    # 辅助方法
    def _generate_random_candidates(self, count: int) -> List[str]:
        """生成随机候选"""
        return [self._generate_random_candidate() for _ in range(count)]
    
    def _generate_random_candidate(self) -> str:
        """生成单个随机候选"""
        return ''.join([str(random.randint(0, 9)) for _ in range(4)])
    
    def _analyze_position_frequencies(self, data: List[SplitRecord]) -> Dict[str, Dict[int, float]]:
        """分析各位置数字频率"""
        frequencies = defaultdict(lambda: defaultdict(int))
        
        for record in data:
            frequencies['千位'][record.thousands] += 1
            frequencies['百位'][record.hundreds] += 1
            frequencies['十位'][record.tens] += 1
            frequencies['个位'][record.units] += 1
        
        # 转换为概率
        for pos in frequencies:
            total = sum(frequencies[pos].values())
            if total > 0:
                for digit in frequencies[pos]:
                    frequencies[pos][digit] = frequencies[pos][digit] / total
        
        return dict(frequencies)
    
    def _extract_patterns(self, data: List[SplitRecord]) -> List[str]:
        """提取历史模式"""
        patterns = []
        for record in data[-20:]:  # 使用最近20期
            pattern = f"{record.thousands}{record.hundreds}{record.tens}{record.units}"
            patterns.append(pattern)
        return patterns
    
    def _mutate_pattern(self, pattern: str) -> str:
        """对模式进行变异"""
        if len(pattern) != 4:
            return self._generate_random_candidate()
        
        # 随机改变1-2个位置
        pattern_list = list(pattern)
        positions_to_change = random.sample(range(4), random.randint(1, 2))
        
        for pos in positions_to_change:
            pattern_list[pos] = str(random.randint(0, 9))
        
        return ''.join(pattern_list)
    
    def _calculate_position_statistics(self, data: List[SplitRecord]) -> Dict[str, Dict[str, float]]:
        """计算位置统计信息"""
        stats = {}
        
        positions = {
            '千位': [record.thousands for record in data],
            '百位': [record.hundreds for record in data],
            '十位': [record.tens for record in data],
            '个位': [record.units for record in data]
        }
        
        for pos, values in positions.items():
            if values:
                stats[pos] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': min(values),
                    'max': max(values)
                }
        
        return stats
    
    def _analyze_trends(self, data: List[SplitRecord]) -> Dict[str, float]:
        """分析趋势"""
        trends = {}
        
        positions = {
            '千位': [record.thousands for record in data],
            '百位': [record.hundreds for record in data],
            '十位': [record.tens for record in data],
            '个位': [record.units for record in data]
        }
        
        for pos, values in positions.items():
            if len(values) >= 2:
                # 计算简单的线性趋势
                x = list(range(len(values)))
                trend = np.polyfit(x, values, 1)[0]  # 线性回归斜率
                trends[pos] = trend
        
        return trends
    
    def _get_important_combinations(self, data: List[SplitRecord]) -> List[List[int]]:
        """获取重要的数字组合"""
        # 简化实现：返回一些常见的组合模式
        combinations = [
            [0, 1, 2, 3], [1, 2, 3, 4], [2, 3, 4, 5],
            [5, 6, 7, 8], [6, 7, 8, 9], [0, 2, 4, 6],
            [1, 3, 5, 7], [0, 5, 0, 5], [1, 4, 7, 0]
        ]
        return combinations
    
    def _build_candidate_from_combination(self, combo: List[int]) -> str:
        """从组合构建候选"""
        if len(combo) >= 4:
            return ''.join([str(combo[i]) for i in range(4)])
        else:
            # 补充随机数字
            result = [str(d) for d in combo]
            while len(result) < 4:
                result.append(str(random.randint(0, 9)))
            return ''.join(result)
    
    def _analyze_digit_gaps(self, data: List[SplitRecord]) -> Dict[str, Dict[int, int]]:
        """分析数字间隔"""
        gaps = defaultdict(lambda: defaultdict(int))
        
        positions = {
            '千位': [record.thousands for record in data],
            '百位': [record.hundreds for record in data],
            '十位': [record.tens for record in data],
            '个位': [record.units for record in data]
        }
        
        for pos, values in positions.items():
            # 计算每个数字的最后出现间隔
            for digit in range(10):
                gap = 0
                for i in range(len(values) - 1, -1, -1):
                    if values[i] == digit:
                        break
                    gap += 1
                gaps[pos][digit] = gap + 1  # +1避免权重为0
        
        return dict(gaps)
    
    def _analyze_position_correlations(self, data: List[SplitRecord]) -> Dict[str, float]:
        """分析位置间相关性"""
        correlations = {}
        
        if len(data) < 10:
            return correlations
        
        positions = {
            '千位': [record.thousands for record in data],
            '百位': [record.hundreds for record in data],
            '十位': [record.tens for record in data],
            '个位': [record.units for record in data]
        }
        
        pos_names = list(positions.keys())
        for i in range(len(pos_names)):
            for j in range(i + 1, len(pos_names)):
                pos1, pos2 = pos_names[i], pos_names[j]
                corr = np.corrcoef(positions[pos1], positions[pos2])[0, 1]
                if not np.isnan(corr):
                    correlations[f"{pos1}_{pos2}"] = corr
        
        return correlations
    
    def _generate_correlated_candidate(self, correlations: Dict[str, float]) -> str:
        """基于相关性生成候选"""
        # 简化实现：生成随机候选
        return self._generate_random_candidate()
    
    def _calculate_performance_metrics(self, data: List[SplitRecord]) -> Dict[str, float]:
        """计算性能指标"""
        # 简化实现：返回基本指标
        return {
            'volatility': 1.0,
            'trend_strength': 0.5,
            'pattern_consistency': 0.7
        }
    
    def _adaptive_sample_candidate(self, metrics: Dict[str, float]) -> str:
        """自适应采样候选"""
        # 简化实现：基于指标调整随机性
        return self._generate_random_candidate()
    
    def _generate_intelligent_supplement(self, data: List[SplitRecord], current_round: int,
                                       features: Feature, needed: int, 
                                       existing: List[str]) -> List[str]:
        """智能补充策略"""
        supplements = []
        existing_set = set(existing)
        
        # 使用多种补充策略
        strategies = [
            lambda: self._generate_random_candidate(),
            lambda: self._generate_balanced_candidate(),
            lambda: self._generate_extreme_candidate(),
        ]
        
        for _ in range(needed):
            for _ in range(10):  # 最多尝试10次
                strategy = random.choice(strategies)
                candidate = strategy()
                if candidate not in existing_set:
                    supplements.append(candidate)
                    existing_set.add(candidate)
                    break
            else:
                # 如果10次都没成功，强制生成一个
                supplements.append(self._generate_random_candidate())
        
        return supplements
    
    def _generate_balanced_candidate(self) -> str:
        """生成平衡的候选（数字分布均匀）"""
        digits = list(range(10))
        random.shuffle(digits)
        return ''.join([str(digits[i]) for i in range(4)])
    
    def _generate_extreme_candidate(self) -> str:
        """生成极值候选（偏向极大或极小值）"""
        if random.random() < 0.5:
            # 偏向小值
            return ''.join([str(random.randint(0, 3)) for _ in range(4)])
        else:
            # 偏向大值
            return ''.join([str(random.randint(6, 9)) for _ in range(4)])


class CandidateCoverageAnalyzer:
    """候选覆盖率分析器"""

    def __init__(self):
        self.total_possible = 10000  # 4位数字的总可能组合数

    def analyze_coverage(self, candidates: List[str]) -> Dict[str, float]:
        """分析候选覆盖率"""
        analysis = {
            'total_candidates': len(candidates),
            'unique_candidates': len(set(candidates)),
            'coverage_rate': len(set(candidates)) / self.total_possible,
            'position_coverage': self._analyze_position_coverage(candidates),
            'digit_distribution': self._analyze_digit_distribution(candidates),
            'pattern_diversity': self._analyze_pattern_diversity(candidates),
        }
        return analysis

    def _analyze_position_coverage(self, candidates: List[str]) -> Dict[str, Dict[int, int]]:
        """分析各位置数字覆盖情况"""
        coverage = {
            '千位': defaultdict(int),
            '百位': defaultdict(int),
            '十位': defaultdict(int),
            '个位': defaultdict(int)
        }

        for candidate in candidates:
            if len(candidate) == 4:
                coverage['千位'][int(candidate[0])] += 1
                coverage['百位'][int(candidate[1])] += 1
                coverage['十位'][int(candidate[2])] += 1
                coverage['个位'][int(candidate[3])] += 1

        return {pos: dict(counts) for pos, counts in coverage.items()}

    def _analyze_digit_distribution(self, candidates: List[str]) -> Dict[str, float]:
        """分析数字分布均匀性"""
        all_digits = []
        for candidate in candidates:
            all_digits.extend([int(d) for d in candidate if d.isdigit()])

        if not all_digits:
            return {'uniformity': 0.0, 'entropy': 0.0}

        # 计算分布均匀性
        digit_counts = defaultdict(int)
        for digit in all_digits:
            digit_counts[digit] += 1

        total = len(all_digits)
        expected_freq = total / 10  # 期望频率

        # 计算均匀性（越接近1越均匀）
        variance = sum((count - expected_freq) ** 2 for count in digit_counts.values()) / 10
        uniformity = 1.0 / (1.0 + variance / expected_freq)

        # 计算熵
        probabilities = [count / total for count in digit_counts.values()]
        entropy = -sum(p * np.log2(p) for p in probabilities if p > 0)

        return {
            'uniformity': uniformity,
            'entropy': entropy,
            'max_entropy': np.log2(10)  # 最大可能熵
        }

    def _analyze_pattern_diversity(self, candidates: List[str]) -> Dict[str, float]:
        """分析模式多样性"""
        if not candidates:
            return {'diversity_score': 0.0}

        # 分析不同类型的模式
        patterns = {
            'ascending': 0,      # 递增
            'descending': 0,     # 递减
            'repeated': 0,       # 重复数字
            'alternating': 0,    # 交替模式
            'random': 0          # 随机模式
        }

        for candidate in candidates:
            if len(candidate) == 4:
                digits = [int(d) for d in candidate]

                # 检查递增
                if digits == sorted(digits):
                    patterns['ascending'] += 1
                # 检查递减
                elif digits == sorted(digits, reverse=True):
                    patterns['descending'] += 1
                # 检查重复
                elif len(set(digits)) < 4:
                    patterns['repeated'] += 1
                # 检查交替（简化版）
                elif abs(digits[0] - digits[2]) <= 1 and abs(digits[1] - digits[3]) <= 1:
                    patterns['alternating'] += 1
                else:
                    patterns['random'] += 1

        total = len(candidates)
        pattern_ratios = {k: v / total for k, v in patterns.items()}

        # 计算多样性分数（熵）
        diversity_score = -sum(ratio * np.log2(ratio) for ratio in pattern_ratios.values() if ratio > 0)

        return {
            'diversity_score': diversity_score,
            'pattern_ratios': pattern_ratios
        }

    def generate_coverage_report(self, candidates: List[str]) -> str:
        """生成覆盖率报告"""
        analysis = self.analyze_coverage(candidates)

        report = f"""
📊 候选覆盖率分析报告
{'='*50}
总候选数量: {analysis['total_candidates']}
唯一候选数量: {analysis['unique_candidates']}
覆盖率: {analysis['coverage_rate']:.4f} ({analysis['coverage_rate']*100:.2f}%)

📈 数字分布分析:
均匀性: {analysis['digit_distribution']['uniformity']:.4f}
熵值: {analysis['digit_distribution']['entropy']:.4f} / {analysis['digit_distribution']['max_entropy']:.4f}

🎯 模式多样性:
多样性分数: {analysis['pattern_diversity']['diversity_score']:.4f}
模式分布: {analysis['pattern_diversity']['pattern_ratios']}

📍 位置覆盖情况:
"""

        for pos, coverage in analysis['position_coverage'].items():
            covered_digits = len([d for d, count in coverage.items() if count > 0])
            report += f"{pos}: {covered_digits}/10 数字被覆盖\n"

        return report


def integrate_enhanced_candidates(data: List[SplitRecord], current_round: int,
                                features: Feature, target_count: int = 10000) -> Tuple[List[str], Dict[str, float]]:
    """集成增强候选生成"""
    generator = EnhancedCandidateGenerator()
    analyzer = CandidateCoverageAnalyzer()

    # 生成候选
    candidates = generator.generate_comprehensive_candidates(data, current_round, features, target_count)

    # 分析覆盖率
    coverage_analysis = analyzer.analyze_coverage(candidates)

    # 生成报告
    report = analyzer.generate_coverage_report(candidates)
    logger.info(f"📊 覆盖率分析报告:\n{report}")

    return candidates, coverage_analysis


if __name__ == "__main__":
    # 测试增强候选生成器
    logger.info("🧪 测试增强候选生成器")

    generator = EnhancedCandidateGenerator()
    analyzer = CandidateCoverageAnalyzer()

    # 创建测试数据
    test_data = []
    for i in range(1, 21):
        record = SplitRecord(
            round=i,
            thousands=random.randint(0, 9),
            hundreds=random.randint(0, 9),
            tens=random.randint(0, 9),
            units=random.randint(0, 9)
        )
        test_data.append(record)

    # 创建测试特征
    test_features = Feature(features={'test_feature': 1.0})

    # 生成候选
    candidates = generator.generate_comprehensive_candidates(
        test_data, 21, test_features, 1000
    )

    # 分析覆盖率
    coverage_report = analyzer.generate_coverage_report(candidates)

    logger.info(f"✅ 测试完成，生成候选数量: {len(candidates)}")
    logger.info(f"📊 覆盖率报告:\n{coverage_report}")
    logger.info(f"🎯 候选样例: {candidates[:10]}")
