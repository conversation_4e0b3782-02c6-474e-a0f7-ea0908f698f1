# 原始候选结构保存与验证分析

## 概述

本功能用于保存系统生成的原始1000个候选结构，并提供验证分析工具来检查这些结构中是否存在真实命中的结构。

## 功能特点

### 1. 自动保存原始结构
- 系统运行时自动保存生成的1000个原始候选结构
- 保存位置：`logs/raw_candidates/` 目录
- 文件格式：JSON格式，包含完整的元数据信息

### 2. 验证分析工具
- 专门的分析器用于验证原始结构的命中情况
- 与真实开奖数据进行比对
- 生成详细的分析报告

## 使用流程

### 步骤1：运行系统生成结构

使用GUI界面运行系统：
1. 启动GUI界面
2. 设置训练期数（如1-150期）
3. 设置预测期号（如第151期）
4. 点击运行预测

系统会自动：
- 生成1000个候选结构
- 保存原始结构到 `logs/raw_candidates/candidates_round_151_YYYYMMDD_HHMMSS.json`
- 进行融合评分，输出Top20结果到 `logs/prediction_results.csv`

### 步骤2：验证分析

运行验证分析器：

```bash
# 分析最新生成的候选结构文件
python tools/analyze_raw_candidates.py

# 分析指定期号的候选结构
python tools/analyze_raw_candidates.py --period 151

# 分析指定文件
python tools/analyze_raw_candidates.py --file logs/raw_candidates/candidates_round_151_20250602_135538.json

# 指定输出目录
python tools/analyze_raw_candidates.py --output-dir analysis/my_analysis
```

## 文件格式

### 原始候选结构文件格式

```json
{
  "round": 151,
  "timestamp": "2025-06-02T13:55:38.123456",
  "count": 1000,
  "expected_count": 1000,
  "candidates": [
    "1234",
    "5678",
    "9012",
    ...
  ],
  "metadata": {
    "generation_method": "strategy_based",
    "description": "原始候选结构 - 第151期预测",
    "format": "4位数字字符串列表"
  }
}
```

### 分析报告格式

```json
{
  "analysis_info": {
    "analysis_time": "2025-06-02T14:00:00.000000",
    "period": 151,
    "candidates_file_timestamp": "2025-06-02T13:55:38.123456",
    "analyzer_version": "1.0.0"
  },
  "candidates_info": {
    "total_count": 1000,
    "expected_count": 1000,
    "generation_method": "strategy_based",
    "unique_count": 995,
    "duplicate_count": 5
  },
  "real_data_info": {
    "period": 151,
    "total_winning_numbers": 23,
    "winning_structures": ["1234", "5678", ...]
  },
  "hit_analysis": {
    "hit_count": 3,
    "hit_rate": 0.003,
    "coverage_rate": 0.1304,
    "hit_structures": ["1234", "5678", "9012"],
    "missed_structures": ["2345", "6789", ...]
  },
  "summary": {
    "success": true,
    "performance_rating": "良好",
    "key_findings": [
      "成功命中 3 个真实开奖结构",
      "命中率为 0.30%",
      "覆盖了 13.04% 的真实开奖号码"
    ]
  }
}
```

## 性能评级标准

- **优秀**：命中率 ≥ 5%
- **良好**：命中率 ≥ 2%
- **一般**：命中率 ≥ 1%
- **较差**：命中率 > 0%
- **失败**：命中率 = 0%

## 输出示例

```
=== 第151期候选结构验证分析结果 ===
候选结构总数: 1000
真实开奖结构数: 23
命中结构数: 3
命中率: 0.30%
覆盖率: 13.04%
命中的结构: 1234, 5678, 9012
性能评级: 良好
分析报告: analysis/raw_candidates/analysis_report_period_151_20250602_140000.json
```

## 目录结构

```
logs/
├── raw_candidates/                    # 原始候选结构目录
│   ├── candidates_round_151_20250602_135538.json
│   ├── candidates_round_152_20250602_140000.json
│   └── ...
└── prediction_results.csv            # 融合评分后的Top20结果

analysis/
└── raw_candidates/                    # 分析报告目录
    ├── analysis_report_period_151_20250602_140000.json
    ├── analysis_report_period_152_20250602_140500.json
    └── ...
```

## 注意事项

1. **数据完整性**：确保历史数据文件包含要验证期号的真实开奖数据
2. **文件权限**：确保程序有权限在 `logs/` 和 `analysis/` 目录创建文件
3. **存储空间**：每个候选结构文件约占用几KB空间，注意定期清理旧文件
4. **时间戳**：文件名包含时间戳，避免重复运行时文件覆盖

## 故障排除

### 常见问题

1. **找不到候选结构文件**
   - 检查 `logs/raw_candidates/` 目录是否存在
   - 确认系统已成功运行并生成结构

2. **找不到真实开奖数据**
   - 检查历史数据文件是否包含指定期号的数据
   - 确认数据格式正确

3. **分析报告保存失败**
   - 检查 `analysis/` 目录权限
   - 确认磁盘空间充足

### 调试模式

运行时添加详细日志：
```bash
python tools/analyze_raw_candidates.py --period 151 2>&1 | tee analysis.log
```

## 更新日志

- **v1.0.0** (2025-06-02)
  - 初始版本
  - 支持原始候选结构保存
  - 支持验证分析和报告生成
  - 支持多种命令行参数
