#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化集成测试 - 验证核心组件功能
"""

import os
import sys
import traceback

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

def test_imports():
    """测试核心模块导入"""
    print("🧪 测试模块导入...")
    
    try:
        # 测试配置模块
        from src.config import get_config
        print("✅ 配置模块导入成功")
        
        # 测试特征生成模块
        from src.data.optimized_feature_generator import OptimizedFeatureGenerator
        print("✅ 优化特征生成器导入成功")
        
        # 测试候选生成模块
        from src.right_brain.enhanced_candidate_generator import EnhancedCandidateGenerator
        print("✅ 增强候选生成器导入成功")
        
        # 测试评分系统模块
        from src.left_brain.enhanced_scoring_system import EnhancedScoringSystem
        print("✅ 增强评分系统导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试基本功能...")
    
    try:
        # 测试配置读取
        from src.config import get_config
        config = get_config()
        print(f"✅ 配置读取成功，配置项数量: {len(config)}")
        
        # 测试特征生成器创建
        from src.data.optimized_feature_generator import OptimizedFeatureGenerator
        generator = OptimizedFeatureGenerator()
        print("✅ 特征生成器创建成功")
        
        # 测试候选生成器创建
        from src.right_brain.enhanced_candidate_generator import EnhancedCandidateGenerator
        candidate_gen = EnhancedCandidateGenerator()
        print("✅ 候选生成器创建成功")
        
        # 测试评分系统创建
        from src.left_brain.enhanced_scoring_system import EnhancedScoringSystem
        scoring_system = EnhancedScoringSystem()
        print("✅ 评分系统创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_config_validation():
    """测试配置验证"""
    print("\n🧪 测试配置验证...")
    
    try:
        from src.config.config_validator import ConfigValidator
        validator = ConfigValidator()
        issues = validator.validate_all_configs()
        
        if issues:
            print(f"⚠️ 发现配置问题: {issues}")
        else:
            print("✅ 配置验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return False

def test_feature_generation():
    """测试特征生成"""
    print("\n🧪 测试特征生成...")
    
    try:
        import polars as pl
        from src.data.optimized_feature_generator import generate_optimized_features
        
        # 创建测试数据
        test_data = pl.DataFrame({
            "期号": list(range(1, 21)),
            "千位": [1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0],
            "百位": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
            "十位": [9, 8, 7, 6, 5, 4, 3, 2, 1, 0, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0],
            "个位": [5, 5, 5, 5, 5, 6, 6, 6, 6, 6, 7, 7, 7, 7, 7, 8, 8, 8, 8, 8]
        })
        
        # 生成特征
        features_df = generate_optimized_features(test_data, 21, ["1234", "5678"])
        
        if not features_df.is_empty():
            print(f"✅ 特征生成成功，生成 {features_df.height} 个结构的特征")
            sample_features = features_df[0, 'features']
            print(f"   特征数量: {len(sample_features)}")
        else:
            print("❌ 特征生成失败，返回空DataFrame")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 特征生成测试失败: {e}")
        return False

def test_scoring_system():
    """测试评分系统"""
    print("\n🧪 测试评分系统...")
    
    try:
        import numpy as np
        from src.left_brain.enhanced_scoring_system import enhanced_score_structures
        
        # 创建测试数据
        test_structures = ["1234", "5678", "9012"]
        test_rule_scores = {s: np.random.random() for s in test_structures}
        test_model_scores = {s: np.random.random() for s in test_structures}
        test_features = {
            'freq5_k_1': 0.2, 'freq5_k_2': 0.3,
            'mean_k': 4.5, 'std_k': 2.8
        }
        
        # 测试评分
        scores = enhanced_score_structures(
            test_structures, test_rule_scores, test_model_scores,
            features=test_features, use_enhanced=True
        )
        
        if scores and len(scores) == len(test_structures):
            print(f"✅ 评分系统测试成功，评分 {len(scores)} 个结构")
            print(f"   平均分数: {np.mean(list(scores.values())):.4f}")
        else:
            print("❌ 评分系统测试失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 评分系统测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 开始简化集成测试")
    print("="*50)
    
    tests = [
        ("模块导入", test_imports),
        ("基本功能", test_basic_functionality),
        ("配置验证", test_config_validation),
        ("特征生成", test_feature_generation),
        ("评分系统", test_scoring_system),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "="*50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统集成成功！")
    elif passed >= total * 0.8:
        print("✅ 大部分测试通过，系统基本可用")
    else:
        print("⚠️ 多个测试失败，需要检查系统配置")
    
    return passed / total

if __name__ == "__main__":
    success_rate = main()
    exit(0 if success_rate >= 0.8 else 1)
