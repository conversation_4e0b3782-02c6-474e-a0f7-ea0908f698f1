#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强评分系统 - 第四阶段评分系统重设计
实现多维度评分、权重自适应和智能融合机制
"""

import os
import sys
import numpy as np
import polars as pl
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

try:
    from src.utils.logger import get_logger
    from src.data.feature_generator import Feature
    logger = get_logger(__name__)
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)


class ScoringDimension(Enum):
    """评分维度枚举"""
    FREQUENCY = "frequency"          # 频率维度
    PATTERN = "pattern"             # 模式维度
    STATISTICAL = "statistical"     # 统计维度
    TEMPORAL = "temporal"           # 时序维度
    CORRELATION = "correlation"     # 相关性维度
    ENSEMBLE = "ensemble"           # 集成维度


@dataclass
class ScoringMetrics:
    """评分指标"""
    accuracy: float = 0.0           # 准确性
    consistency: float = 0.0        # 一致性
    diversity: float = 0.0          # 多样性
    confidence: float = 0.0         # 置信度
    stability: float = 0.0          # 稳定性


class AdaptiveWeightManager:
    """自适应权重管理器"""
    
    def __init__(self):
        # 初始权重配置
        self.base_weights = {
            ScoringDimension.FREQUENCY: 0.25,
            ScoringDimension.PATTERN: 0.20,
            ScoringDimension.STATISTICAL: 0.20,
            ScoringDimension.TEMPORAL: 0.15,
            ScoringDimension.CORRELATION: 0.10,
            ScoringDimension.ENSEMBLE: 0.10,
        }
        
        # 历史性能记录
        self.performance_history = {dim: [] for dim in ScoringDimension}
        
        # 权重调整参数
        self.learning_rate = 0.1
        self.momentum = 0.9
        self.min_weight = 0.05
        self.max_weight = 0.5
        
    def update_weights(self, performance_metrics: Dict[ScoringDimension, ScoringMetrics]) -> Dict[ScoringDimension, float]:
        """基于性能指标更新权重"""
        new_weights = self.base_weights.copy()
        
        # 计算性能分数
        performance_scores = {}
        for dim, metrics in performance_metrics.items():
            # 综合性能分数
            score = (metrics.accuracy * 0.4 + 
                    metrics.consistency * 0.3 + 
                    metrics.confidence * 0.2 + 
                    metrics.stability * 0.1)
            performance_scores[dim] = score
            
            # 记录历史性能
            self.performance_history[dim].append(score)
            if len(self.performance_history[dim]) > 100:  # 保留最近100次记录
                self.performance_history[dim].pop(0)
        
        # 基于性能调整权重
        total_performance = sum(performance_scores.values())
        if total_performance > 0:
            for dim in ScoringDimension:
                # 计算相对性能
                relative_performance = performance_scores.get(dim, 0) / total_performance
                
                # 权重调整
                current_weight = self.base_weights[dim]
                target_weight = relative_performance * len(ScoringDimension)
                
                # 使用动量更新
                adjustment = self.learning_rate * (target_weight - current_weight)
                new_weight = current_weight + adjustment
                
                # 权重约束
                new_weight = max(self.min_weight, min(self.max_weight, new_weight))
                new_weights[dim] = new_weight
        
        # 归一化权重
        total_weight = sum(new_weights.values())
        if total_weight > 0:
            new_weights = {dim: weight / total_weight for dim, weight in new_weights.items()}
        
        # 更新基础权重
        self.base_weights = new_weights
        
        logger.info(f"🔄 权重更新完成: {new_weights}")
        return new_weights
    
    def get_current_weights(self) -> Dict[ScoringDimension, float]:
        """获取当前权重"""
        return self.base_weights.copy()


class MultiDimensionalScorer:
    """多维度评分器"""
    
    def __init__(self):
        self.weight_manager = AdaptiveWeightManager()
        
    def score_frequency_dimension(self, structures: List[str], features: Dict[str, Any]) -> Dict[str, float]:
        """频率维度评分"""
        scores = {}
        
        for structure in structures:
            score = 0.0
            
            # 基于频率特征计算分数
            for feature_name, feature_value in features.items():
                if 'freq' in feature_name.lower():
                    # 频率特征权重
                    if 'freq5' in feature_name:
                        score += feature_value * 1.0  # 短期频率权重最高
                    elif 'freq10' in feature_name:
                        score += feature_value * 0.8  # 中期频率
                    elif 'freq20' in feature_name:
                        score += feature_value * 0.6  # 长期频率
            
            scores[structure] = max(0.0, min(1.0, score))  # 归一化到[0,1]
        
        return scores
    
    def score_pattern_dimension(self, structures: List[str], features: Dict[str, Any]) -> Dict[str, float]:
        """模式维度评分"""
        scores = {}
        
        for structure in structures:
            score = 0.0
            
            # 基于模式特征计算分数
            for feature_name, feature_value in features.items():
                if any(pattern in feature_name.lower() for pattern in ['consecutive', 'missing', 'pattern']):
                    # 模式特征权重
                    if 'consecutive' in feature_name:
                        score += (1.0 - feature_value / 10.0) * 0.3  # 连续性，值越小越好
                    elif 'missing' in feature_name:
                        score += min(feature_value / 20.0, 1.0) * 0.4  # 缺失期数，适中最好
            
            scores[structure] = max(0.0, min(1.0, score))
        
        return scores
    
    def score_statistical_dimension(self, structures: List[str], features: Dict[str, Any]) -> Dict[str, float]:
        """统计维度评分"""
        scores = {}
        
        for structure in structures:
            score = 0.0
            
            # 基于统计特征计算分数
            for feature_name, feature_value in features.items():
                if any(stat in feature_name.lower() for stat in ['mean', 'std', 'var', 'skew', 'kurt']):
                    # 统计特征权重
                    if 'mean' in feature_name:
                        # 均值接近4.5最好
                        score += (1.0 - abs(feature_value - 4.5) / 4.5) * 0.2
                    elif 'std' in feature_name:
                        # 标准差适中最好
                        score += (1.0 - abs(feature_value - 2.87) / 2.87) * 0.3
            
            scores[structure] = max(0.0, min(1.0, score))
        
        return scores
    
    def score_temporal_dimension(self, structures: List[str], features: Dict[str, Any]) -> Dict[str, float]:
        """时序维度评分"""
        scores = {}
        
        for structure in structures:
            score = 0.0
            
            # 基于时序特征计算分数
            for feature_name, feature_value in features.items():
                if any(temporal in feature_name.lower() for temporal in ['lag', 'diff', 'ma']):
                    # 时序特征权重
                    if 'lag' in feature_name:
                        score += abs(feature_value) / 10.0 * 0.2
                    elif 'diff' in feature_name:
                        score += (1.0 - abs(feature_value) / 5.0) * 0.3
                    elif 'ma' in feature_name:
                        score += abs(feature_value - 4.5) / 4.5 * 0.2
            
            scores[structure] = max(0.0, min(1.0, score))
        
        return scores
    
    def score_correlation_dimension(self, structures: List[str], features: Dict[str, Any]) -> Dict[str, float]:
        """相关性维度评分"""
        scores = {}
        
        for structure in structures:
            score = 0.5  # 基础分数
            
            # 基于相关性特征计算分数（简化实现）
            if len(structure) == 4:
                digits = [int(d) for d in structure]
                
                # 位置间相关性分析
                correlations = []
                for i in range(len(digits) - 1):
                    corr = abs(digits[i] - digits[i + 1]) / 9.0
                    correlations.append(corr)
                
                if correlations:
                    avg_correlation = sum(correlations) / len(correlations)
                    score = 1.0 - avg_correlation  # 相关性越低分数越高
            
            scores[structure] = max(0.0, min(1.0, score))
        
        return scores
    
    def score_ensemble_dimension(self, structures: List[str], other_scores: Dict[ScoringDimension, Dict[str, float]]) -> Dict[str, float]:
        """集成维度评分"""
        scores = {}
        
        for structure in structures:
            # 计算其他维度分数的方差（多样性指标）
            structure_scores = []
            for dim, dim_scores in other_scores.items():
                if structure in dim_scores:
                    structure_scores.append(dim_scores[structure])
            
            if structure_scores:
                # 方差越大，多样性越好
                variance = np.var(structure_scores)
                score = min(variance * 4.0, 1.0)  # 缩放到[0,1]
            else:
                score = 0.5
            
            scores[structure] = score
        
        return scores
    
    def compute_multi_dimensional_scores(self, structures: List[str], features: Dict[str, Any]) -> Dict[str, float]:
        """计算多维度综合评分"""
        logger.info(f"🎯 开始多维度评分，结构数量: {len(structures)}")
        
        # 各维度评分
        dimension_scores = {}
        
        # 1. 频率维度
        dimension_scores[ScoringDimension.FREQUENCY] = self.score_frequency_dimension(structures, features)
        
        # 2. 模式维度
        dimension_scores[ScoringDimension.PATTERN] = self.score_pattern_dimension(structures, features)
        
        # 3. 统计维度
        dimension_scores[ScoringDimension.STATISTICAL] = self.score_statistical_dimension(structures, features)
        
        # 4. 时序维度
        dimension_scores[ScoringDimension.TEMPORAL] = self.score_temporal_dimension(structures, features)
        
        # 5. 相关性维度
        dimension_scores[ScoringDimension.CORRELATION] = self.score_correlation_dimension(structures, features)
        
        # 6. 集成维度
        other_dims = {k: v for k, v in dimension_scores.items() if k != ScoringDimension.ENSEMBLE}
        dimension_scores[ScoringDimension.ENSEMBLE] = self.score_ensemble_dimension(structures, other_dims)
        
        # 获取当前权重
        weights = self.weight_manager.get_current_weights()
        
        # 加权融合
        final_scores = {}
        for structure in structures:
            weighted_score = 0.0
            total_weight = 0.0
            
            for dim, dim_scores in dimension_scores.items():
                if structure in dim_scores:
                    weight = weights.get(dim, 0.0)
                    weighted_score += dim_scores[structure] * weight
                    total_weight += weight
            
            if total_weight > 0:
                final_scores[structure] = weighted_score / total_weight
            else:
                final_scores[structure] = 0.5  # 默认分数
        
        logger.info(f"✅ 多维度评分完成，平均分数: {np.mean(list(final_scores.values())):.4f}")
        return final_scores


class EnhancedScoringSystem:
    """增强评分系统"""
    
    def __init__(self):
        self.multi_dimensional_scorer = MultiDimensionalScorer()
        self.performance_tracker = {}
        
    def enhanced_score_fusion(self, rule_scores: Dict[str, float], 
                            model_scores: Dict[str, float],
                            mlp_scores: Dict[str, float],
                            features: Dict[str, Any],
                            adaptive_weights: bool = True) -> Dict[str, float]:
        """增强评分融合"""
        logger.info("🚀 开始增强评分融合")
        
        # 获取所有结构
        all_structures = set(rule_scores.keys()) | set(model_scores.keys()) | set(mlp_scores.keys())
        structures = list(all_structures)
        
        # 1. 多维度评分
        multi_dim_scores = self.multi_dimensional_scorer.compute_multi_dimensional_scores(structures, features)
        
        # 2. 传统评分归一化
        normalized_rule_scores = self._normalize_scores(rule_scores)
        normalized_model_scores = self._normalize_scores(model_scores)
        normalized_mlp_scores = self._normalize_scores(mlp_scores)
        
        # 3. 智能权重分配
        if adaptive_weights:
            # 基于历史性能自适应调整权重
            fusion_weights = self._calculate_adaptive_fusion_weights()
        else:
            # 使用固定权重
            fusion_weights = {
                'rule': 0.15,
                'model': 0.35,
                'mlp': 0.30,
                'multi_dim': 0.20
            }
        
        # 4. 加权融合
        final_scores = {}
        for structure in structures:
            score = 0.0
            total_weight = 0.0
            
            # 规则分数
            if structure in normalized_rule_scores:
                score += normalized_rule_scores[structure] * fusion_weights['rule']
                total_weight += fusion_weights['rule']
            
            # 模型分数
            if structure in normalized_model_scores:
                score += normalized_model_scores[structure] * fusion_weights['model']
                total_weight += fusion_weights['model']
            
            # MLP分数
            if structure in normalized_mlp_scores:
                score += normalized_mlp_scores[structure] * fusion_weights['mlp']
                total_weight += fusion_weights['mlp']
            
            # 多维度分数
            if structure in multi_dim_scores:
                score += multi_dim_scores[structure] * fusion_weights['multi_dim']
                total_weight += fusion_weights['multi_dim']
            
            # 归一化
            if total_weight > 0:
                final_scores[structure] = score / total_weight
            else:
                final_scores[structure] = 0.5
        
        logger.info(f"✅ 增强评分融合完成，使用权重: {fusion_weights}")
        return final_scores
    
    def _normalize_scores(self, scores: Dict[str, float]) -> Dict[str, float]:
        """分数归一化"""
        if not scores:
            return {}
        
        values = list(scores.values())
        min_val = min(values)
        max_val = max(values)
        
        if max_val == min_val:
            return {k: 0.5 for k in scores.keys()}
        
        normalized = {}
        for k, v in scores.items():
            normalized[k] = (v - min_val) / (max_val - min_val)
        
        return normalized
    
    def _calculate_adaptive_fusion_weights(self) -> Dict[str, float]:
        """计算自适应融合权重"""
        # 简化实现：基于历史性能调整权重
        base_weights = {
            'rule': 0.15,
            'model': 0.35,
            'mlp': 0.30,
            'multi_dim': 0.20
        }
        
        # 这里可以添加基于历史性能的权重调整逻辑
        # 目前返回基础权重
        return base_weights
    
    def evaluate_scoring_performance(self, predicted_scores: Dict[str, float], 
                                   actual_results: List[str]) -> ScoringMetrics:
        """评估评分性能"""
        if not predicted_scores or not actual_results:
            return ScoringMetrics()
        
        # 计算准确性（简化实现）
        top_predictions = sorted(predicted_scores.items(), key=lambda x: x[1], reverse=True)[:10]
        top_structures = [item[0] for item in top_predictions]
        
        hits = len(set(top_structures) & set(actual_results))
        accuracy = hits / len(actual_results) if actual_results else 0.0
        
        # 计算一致性（分数分布的稳定性）
        scores = list(predicted_scores.values())
        consistency = 1.0 - (np.std(scores) / np.mean(scores)) if np.mean(scores) > 0 else 0.0
        
        # 计算多样性
        diversity = len(set(top_structures)) / len(top_structures) if top_structures else 0.0
        
        # 计算置信度（最高分与平均分的比值）
        if scores:
            max_score = max(scores)
            avg_score = np.mean(scores)
            confidence = max_score / avg_score if avg_score > 0 else 0.0
        else:
            confidence = 0.0
        
        # 计算稳定性（简化为一致性的变体）
        stability = consistency
        
        return ScoringMetrics(
            accuracy=accuracy,
            consistency=max(0.0, min(1.0, consistency)),
            diversity=diversity,
            confidence=min(confidence, 2.0) / 2.0,  # 归一化到[0,1]
            stability=max(0.0, min(1.0, stability))
        )


class ScoringSystemIntegrator:
    """评分系统集成器 - 统一管理新旧评分系统"""

    def __init__(self, use_enhanced: bool = True):
        self.use_enhanced = use_enhanced
        self.enhanced_system = EnhancedScoringSystem() if use_enhanced else None

    def integrated_score_fusion(self, rule_scores: Dict[str, float],
                              model_scores: Dict[str, float],
                              mlp_scores: Optional[Dict[str, float]] = None,
                              features: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """集成评分融合"""
        try:
            if self.use_enhanced and self.enhanced_system and features:
                logger.info("🚀 使用增强评分系统")
                mlp_scores = mlp_scores or {}
                return self.enhanced_system.enhanced_score_fusion(
                    rule_scores, model_scores, mlp_scores, features
                )
            else:
                logger.info("📊 使用传统评分系统")
                return self._traditional_score_fusion(rule_scores, model_scores, mlp_scores)

        except Exception as e:
            logger.error(f"❌ 评分融合失败: {e}")
            # 回退到简单融合
            return self._simple_score_fusion(rule_scores, model_scores, mlp_scores)

    def _traditional_score_fusion(self, rule_scores: Dict[str, float],
                                model_scores: Dict[str, float],
                                mlp_scores: Optional[Dict[str, float]] = None) -> Dict[str, float]:
        """传统评分融合"""
        try:
            # 导入传统融合函数
            from src.left_brain.score_merger import merge_scores_three, merge_scores

            if mlp_scores:
                # 三模型融合
                weights = {"rule": 0.15, "xgb": 0.50, "mlp": 0.35}
                return merge_scores_three(rule_scores, model_scores, mlp_scores, weights)
            else:
                # 双模型融合
                weights = {"rule": 0.3, "model": 0.7}
                return merge_scores(rule_scores, model_scores, weights)

        except Exception as e:
            logger.error(f"❌ 传统评分融合失败: {e}")
            return self._simple_score_fusion(rule_scores, model_scores, mlp_scores)

    def _simple_score_fusion(self, rule_scores: Dict[str, float],
                           model_scores: Dict[str, float],
                           mlp_scores: Optional[Dict[str, float]] = None) -> Dict[str, float]:
        """简单评分融合（回退方案）"""
        all_structures = set(rule_scores.keys()) | set(model_scores.keys())
        if mlp_scores:
            all_structures |= set(mlp_scores.keys())

        fused_scores = {}
        for structure in all_structures:
            score = 0.0
            count = 0

            if structure in rule_scores:
                score += rule_scores[structure] * 0.2
                count += 0.2

            if structure in model_scores:
                score += model_scores[structure] * 0.5
                count += 0.5

            if mlp_scores and structure in mlp_scores:
                score += mlp_scores[structure] * 0.3
                count += 0.3

            fused_scores[structure] = score / count if count > 0 else 0.5

        return fused_scores

    def evaluate_and_adapt(self, predicted_scores: Dict[str, float],
                          actual_results: List[str]) -> None:
        """评估并自适应调整"""
        if self.enhanced_system:
            metrics = self.enhanced_system.evaluate_scoring_performance(predicted_scores, actual_results)
            logger.info(f"📊 评分性能指标: {metrics}")

            # 这里可以添加基于性能指标的系统调整逻辑
            if metrics.accuracy < 0.1:  # 准确率过低
                logger.warning("⚠️ 评分准确率过低，建议检查模型和特征")

            if metrics.consistency < 0.3:  # 一致性过低
                logger.warning("⚠️ 评分一致性过低，建议调整权重配置")


def create_scoring_integrator(use_enhanced: bool = True) -> ScoringSystemIntegrator:
    """创建评分系统集成器"""
    return ScoringSystemIntegrator(use_enhanced=use_enhanced)


def enhanced_score_structures(structures: List[str],
                            rule_scores: Dict[str, float],
                            model_scores: Dict[str, float],
                            mlp_scores: Optional[Dict[str, float]] = None,
                            features: Optional[Dict[str, Any]] = None,
                            use_enhanced: bool = True) -> Dict[str, float]:
    """增强结构评分统一接口"""
    integrator = create_scoring_integrator(use_enhanced=use_enhanced)
    return integrator.integrated_score_fusion(rule_scores, model_scores, mlp_scores, features)


if __name__ == "__main__":
    # 测试增强评分系统
    logger.info("🧪 测试增强评分系统")

    # 测试基础系统
    scoring_system = EnhancedScoringSystem()

    # 创建测试数据
    test_structures = ["1234", "5678", "9012", "3456", "7890"]
    test_rule_scores = {s: np.random.random() for s in test_structures}
    test_model_scores = {s: np.random.random() for s in test_structures}
    test_mlp_scores = {s: np.random.random() for s in test_structures}
    test_features = {
        'freq5_k_1': 0.2, 'freq5_k_2': 0.3,
        'mean_k': 4.5, 'std_k': 2.8,
        'consecutive_k': 1.0, 'missing_k_5': 3.0
    }

    # 测试增强评分融合
    enhanced_scores = scoring_system.enhanced_score_fusion(
        test_rule_scores, test_model_scores, test_mlp_scores, test_features
    )

    logger.info(f"✅ 基础系统测试完成，增强评分结果: {enhanced_scores}")

    # 测试集成器
    integrator = create_scoring_integrator(use_enhanced=True)
    integrated_scores = integrator.integrated_score_fusion(
        test_rule_scores, test_model_scores, test_mlp_scores, test_features
    )

    logger.info(f"✅ 集成器测试完成，集成评分结果: {integrated_scores}")

    # 测试性能评估
    test_actual = ["1234", "5678"]
    integrator.evaluate_and_adapt(integrated_scores, test_actual)

    # 测试统一接口
    unified_scores = enhanced_score_structures(
        test_structures, test_rule_scores, test_model_scores,
        test_mlp_scores, test_features, use_enhanced=True
    )

    logger.info(f"✅ 统一接口测试完成，统一评分结果: {unified_scores}")
    logger.info("🎉 增强评分系统测试完成")
