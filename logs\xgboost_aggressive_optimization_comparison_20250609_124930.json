{"comparison": {"optimization_timestamp": "20250609_124930", "previous_results": {"success_rate": 0.0, "average_full_hits": 0.03333333333333333, "average_3digit_matches": 2.466666666666667, "total_full_hits": 1, "periods_meeting_requirement": 0}, "current_results": {"success_rate": 0.0, "average_full_hits": 0.03333333333333333, "average_3digit_matches": 2.466666666666667, "total_full_hits": 1, "periods_meeting_requirement": 0}, "improvements": {"success_rate_change": 0.0, "average_full_hits_change": 0.0, "average_3digit_matches_change": 0.0, "total_full_hits_change": 0, "periods_meeting_requirement_change": 0}, "parameter_changes": {"max_depth": "12 → 8 (降低复杂度)", "learning_rate": "0.02 → 0.1 (大幅提高)", "n_estimators": "1000 → 2000 (增加容量)", "scale_pos_weight": "100.0 → 400.0 (处理不平衡)", "subsample": "0.95 → 0.8 (增加多样性)", "colsample_bytree": "0.95 → 0.8 (增加多样性)", "min_child_weight": "0.1 → 2.0 (增加约束)", "gamma": "0.001 → 0.3 (减少过拟合)", "reg_alpha": "0.001 → 0.05 (特征选择)", "reg_lambda": "0.01 → 0.02 (适度正则化)"}}, "current_full_results": {"test_timestamp": "20250609_124930", "test_type": "xgboost_mock_prediction_diagnostics", "test_config": {"start_period": 301, "end_period": 330, "total_periods": 30, "top30_count": 30, "ground_truth_count": 23}, "summary_statistics": {"total_periods": 30, "periods_meeting_requirement": 0, "periods_not_meeting_requirement": 30, "total_full_hits": 1, "total_3digit_matches": 74, "full_hit_distribution": {"0": 29, "1": 1}, "period_details": [{"period": 301, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 1}, {"period": 302, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 2}, {"period": 303, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 2}, {"period": 304, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 2}, {"period": 305, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 2}, {"period": 306, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 4}, {"period": 307, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 1}, {"period": 308, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 1}, {"period": 309, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 2}, {"period": 310, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 6}, {"period": 311, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 4}, {"period": 312, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 2}, {"period": 313, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 3}, {"period": 314, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 3}, {"period": 315, "full_hits": 1, "meets_requirement": false, "partial_hits_count": 5}, {"period": 316, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 1}, {"period": 317, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 3}, {"period": 318, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 4}, {"period": 319, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 1}, {"period": 320, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 1}, {"period": 321, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 2}, {"period": 322, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 3}, {"period": 323, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 4}, {"period": 324, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 4}, {"period": 325, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 0}, {"period": 326, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 4}, {"period": 327, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 1}, {"period": 328, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 1}, {"period": 329, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 2}, {"period": 330, "full_hits": 0, "meets_requirement": false, "partial_hits_count": 3}], "average_full_hits": 0.03333333333333333, "average_3digit_matches": 2.466666666666667, "success_rate": 0.0}, "all_period_results": {"301": {"period": 301, "predictions": ["4502", "3076", "5494", "1630", "1745", "5054", "3161", "5860", "9374", "4563", "2186", "1641", "6490", "9348", "4454", "3248", "6443", "5733", "3697", "5966", "9762", "3004", "6042", "2362", "4789", "9647", "2863", "4250", "9485", "6714"], "ground_truth": ["3727", "9185", "6044", "2223", "5144", "8778", "6775", "8087", "2570", "1243", "1165", "3107", "3019", "1252", "9989", "4310", "5047", "4593", "9652", "6223", "8861", "8850", "2915"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["604"], "partial_hits_3digit_details": [{"predicted_structure": "6042", "real_structure": "6044", "matched_3digits": "604", "predicted_position": "0-2", "real_position": "0-2"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 1}}, "302": {"period": 302, "predictions": ["6242", "0879", "7490", "4375", "5488", "0287", "2972", "6522", "8819", "5165", "1645", "6943", "4705", "2668", "7959", "5280", "5932", "6368", "4253", "8904", "5273", "5890", "8670", "1594", "7113", "5290", "0796", "3889", "4162", "9782"], "ground_truth": ["6614", "8954", "6645", "9663", "2526", "7140", "9559", "9143", "8041", "7711", "9651", "2061", "2042", "4837", "8228", "5319", "0267", "9852", "5702", "8355", "8845", "9206", "2147"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["645", "711"], "partial_hits_3digit_details": [{"predicted_structure": "1645", "real_structure": "6645", "matched_3digits": "645", "predicted_position": "1-3", "real_position": "1-3"}, {"predicted_structure": "7113", "real_structure": "7711", "matched_3digits": "711", "predicted_position": "0-2", "real_position": "1-3"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 2}}, "303": {"period": 303, "predictions": ["8891", "5814", "2426", "4312", "6354", "0517", "0317", "1535", "4344", "4931", "1217", "5659", "1179", "0375", "1636", "7324", "9478", "5085", "5174", "3803", "3563", "1312", "9314", "7078", "8704", "8793", "3534", "4258", "8037", "6085"], "ground_truth": ["4026", "1352", "4640", "5219", "5052", "4772", "2944", "7307", "2051", "1808", "8227", "4974", "8584", "3823", "7199", "4148", "0204", "9203", "3449", "4503", "0909", "9631", "1105"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["051", "344"], "partial_hits_3digit_details": [{"predicted_structure": "0517", "real_structure": "2051", "matched_3digits": "051", "predicted_position": "0-2", "real_position": "1-3"}, {"predicted_structure": "4344", "real_structure": "3449", "matched_3digits": "344", "predicted_position": "1-3", "real_position": "0-2"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 2}}, "304": {"period": 304, "predictions": ["9895", "4617", "8497", "9980", "4596", "1984", "3307", "9048", "1096", "0517", "4403", "1954", "4362", "2218", "4495", "5129", "2193", "6718", "6046", "2740", "2910", "9164", "6091", "2803", "6545", "2620", "3313", "9324", "0438", "1939"], "ground_truth": ["8398", "1858", "8877", "2911", "2681", "5402", "5669", "8474", "8827", "1507", "8586", "4289", "9385", "7072", "8359", "2926", "9805", "9500", "5923", "3812", "1233", "0445", "5973"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["980", "291"], "partial_hits_3digit_details": [{"predicted_structure": "9980", "real_structure": "9805", "matched_3digits": "980", "predicted_position": "1-3", "real_position": "0-2"}, {"predicted_structure": "2910", "real_structure": "2911", "matched_3digits": "291", "predicted_position": "0-2", "real_position": "0-2"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 2}}, "305": {"period": 305, "predictions": ["0465", "4582", "5341", "5633", "7182", "2262", "8553", "3071", "8835", "3904", "5034", "5248", "1612", "4008", "3822", "5220", "2685", "1262", "8729", "2193", "6041", "4636", "0981", "4557", "2198", "8657", "9816", "3673", "3930", "2268"], "ground_truth": ["5974", "7335", "4260", "0060", "5738", "2286", "6866", "4945", "4799", "8373", "6119", "0633", "6900", "3263", "6860", "7461", "1374", "7435", "5392", "8307", "6897", "7777", "5890"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["633", "307"], "partial_hits_3digit_details": [{"predicted_structure": "5633", "real_structure": "0633", "matched_3digits": "633", "predicted_position": "1-3", "real_position": "1-3"}, {"predicted_structure": "3071", "real_structure": "8307", "matched_3digits": "307", "predicted_position": "0-2", "real_position": "1-3"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 2}}, "306": {"period": 306, "predictions": ["2140", "4535", "9095", "0196", "8321", "2064", "6279", "9275", "4634", "7072", "2208", "8972", "7920", "9556", "2736", "2154", "3085", "6810", "0032", "7685", "1517", "3180", "4106", "7405", "1564", "1237", "0461", "3542", "0518", "5532"], "ground_truth": ["5233", "7518", "3607", "5251", "0897", "1736", "7236", "7939", "8225", "1691", "7622", "2913", "8707", "0385", "7378", "7030", "0070", "1591", "0977", "0674", "6168", "3372", "9718"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["707", "897", "736", "518"], "partial_hits_3digit_details": [{"predicted_structure": "7072", "real_structure": "8707", "matched_3digits": "707", "predicted_position": "0-2", "real_position": "1-3"}, {"predicted_structure": "8972", "real_structure": "0897", "matched_3digits": "897", "predicted_position": "0-2", "real_position": "1-3"}, {"predicted_structure": "2736", "real_structure": "1736", "matched_3digits": "736", "predicted_position": "1-3", "real_position": "1-3"}, {"predicted_structure": "0518", "real_structure": "7518", "matched_3digits": "518", "predicted_position": "1-3", "real_position": "1-3"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 4}}, "307": {"period": 307, "predictions": ["4455", "5077", "7613", "1984", "6911", "8296", "3279", "2765", "0711", "7984", "3447", "9699", "9324", "4819", "9597", "7194", "3733", "6389", "6478", "3368", "8213", "7369", "4959", "5207", "7324", "4633", "4056", "0396", "7282", "9240"], "ground_truth": ["6864", "6949", "4588", "0750", "2748", "4349", "1558", "5698", "7016", "1087", "0951", "4607", "2622", "8484", "0201", "7482", "7952", "0638", "3017", "1965", "6617", "1841", "2385"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["638"], "partial_hits_3digit_details": [{"predicted_structure": "6389", "real_structure": "0638", "matched_3digits": "638", "predicted_position": "0-2", "real_position": "1-3"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 1}}, "308": {"period": 308, "predictions": ["9254", "4867", "9123", "9446", "6722", "8425", "7910", "3726", "1548", "5918", "3392", "8042", "6918", "7398", "0642", "3017", "7032", "0189", "6704", "1174", "2552", "5256", "3307", "6691", "6817", "3941", "1502", "0820", "9399", "1280"], "ground_truth": ["5265", "5429", "7277", "3345", "6079", "8772", "8686", "4922", "4655", "1632", "2306", "3853", "0606", "6404", "8997", "1591", "1864", "4768", "7052", "1206", "5779", "5185", "2309"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["591"], "partial_hits_3digit_details": [{"predicted_structure": "5918", "real_structure": "1591", "matched_3digits": "591", "predicted_position": "0-2", "real_position": "1-3"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 1}}, "309": {"period": 309, "predictions": ["3849", "0085", "9464", "3029", "9164", "0873", "1335", "3405", "4152", "4938", "0570", "1024", "3274", "8296", "6017", "8952", "5674", "5173", "3484", "2305", "8720", "4529", "9467", "3276", "2462", "7016", "1756", "8475", "9637", "3570"], "ground_truth": ["9955", "4549", "7636", "7344", "4587", "1804", "6317", "9422", "5613", "9649", "3582", "0389", "2211", "8837", "7460", "2694", "4836", "8421", "3883", "1543", "6484", "0183", "9305"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["484", "305"], "partial_hits_3digit_details": [{"predicted_structure": "3484", "real_structure": "6484", "matched_3digits": "484", "predicted_position": "1-3", "real_position": "1-3"}, {"predicted_structure": "2305", "real_structure": "9305", "matched_3digits": "305", "predicted_position": "1-3", "real_position": "1-3"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 2}}, "310": {"period": 310, "predictions": ["9423", "6443", "8930", "3517", "0408", "2639", "6977", "2728", "0764", "0310", "0717", "7548", "2328", "5477", "0644", "7011", "5966", "7785", "2555", "4003", "0841", "8834", "7920", "1575", "0389", "9138", "2413", "9358", "9291", "9263"], "ground_truth": ["4214", "2937", "1384", "1112", "2468", "0142", "8076", "2949", "0641", "4525", "9351", "4515", "6259", "8074", "1508", "9236", "0089", "3026", "6623", "0512", "7328", "2797", "9905"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["351", "076", "328", "064", "138", "935"], "partial_hits_3digit_details": [{"predicted_structure": "3517", "real_structure": "9351", "matched_3digits": "351", "predicted_position": "0-2", "real_position": "1-3"}, {"predicted_structure": "0764", "real_structure": "8076", "matched_3digits": "076", "predicted_position": "0-2", "real_position": "1-3"}, {"predicted_structure": "2328", "real_structure": "7328", "matched_3digits": "328", "predicted_position": "1-3", "real_position": "1-3"}, {"predicted_structure": "0644", "real_structure": "0641", "matched_3digits": "064", "predicted_position": "0-2", "real_position": "0-2"}, {"predicted_structure": "9138", "real_structure": "1384", "matched_3digits": "138", "predicted_position": "1-3", "real_position": "0-2"}, {"predicted_structure": "9358", "real_structure": "9351", "matched_3digits": "935", "predicted_position": "0-2", "real_position": "0-2"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 6}}, "311": {"period": 311, "predictions": ["0750", "8949", "0187", "9469", "5545", "3484", "0435", "2045", "7213", "6328", "6016", "7534", "4830", "9659", "3254", "1854", "1146", "5976", "4105", "5764", "1013", "2029", "8259", "3415", "4815", "2798", "4338", "0139", "4128", "7851"], "ground_truth": ["1161", "9768", "1976", "8593", "7514", "0596", "2276", "4467", "8445", "2887", "7025", "0971", "2808", "8938", "5157", "6582", "5819", "1398", "4147", "6538", "7341", "1841", "6359"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["976", "976", "341", "139"], "partial_hits_3digit_details": [{"predicted_structure": "5976", "real_structure": "9768", "matched_3digits": "976", "predicted_position": "1-3", "real_position": "0-2"}, {"predicted_structure": "5976", "real_structure": "1976", "matched_3digits": "976", "predicted_position": "1-3", "real_position": "1-3"}, {"predicted_structure": "3415", "real_structure": "7341", "matched_3digits": "341", "predicted_position": "0-2", "real_position": "1-3"}, {"predicted_structure": "0139", "real_structure": "1398", "matched_3digits": "139", "predicted_position": "1-3", "real_position": "0-2"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 4}}, "312": {"period": 312, "predictions": ["1538", "7484", "0547", "1693", "1988", "3262", "5116", "0584", "1518", "4095", "9842", "1817", "6280", "4686", "5101", "6658", "4849", "9915", "7497", "9703", "8976", "3095", "6341", "3366", "1328", "2770", "7684", "7726", "7383", "4357"], "ground_truth": ["3311", "2014", "3968", "2446", "7249", "3597", "6397", "4882", "9900", "3198", "8578", "9454", "2115", "0853", "8091", "8851", "0470", "1754", "4868", "1112", "5176", "9700", "0968"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["198", "970"], "partial_hits_3digit_details": [{"predicted_structure": "1988", "real_structure": "3198", "matched_3digits": "198", "predicted_position": "0-2", "real_position": "1-3"}, {"predicted_structure": "9703", "real_structure": "9700", "matched_3digits": "970", "predicted_position": "0-2", "real_position": "0-2"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 2}}, "313": {"period": 313, "predictions": ["7509", "4446", "8479", "5683", "1082", "4814", "2451", "2890", "9264", "0904", "3629", "8142", "8749", "5024", "6436", "3672", "9415", "3245", "3616", "9528", "7828", "3861", "9157", "0326", "8810", "2863", "8659", "0148", "2695", "0252"], "ground_truth": ["2196", "5028", "9812", "3689", "9799", "5275", "2755", "1689", "0446", "0920", "2407", "4832", "9614", "9659", "6962", "5516", "3078", "4162", "3591", "3110", "4998", "5168", "2156"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["446", "502", "659"], "partial_hits_3digit_details": [{"predicted_structure": "4446", "real_structure": "0446", "matched_3digits": "446", "predicted_position": "1-3", "real_position": "1-3"}, {"predicted_structure": "5024", "real_structure": "5028", "matched_3digits": "502", "predicted_position": "0-2", "real_position": "0-2"}, {"predicted_structure": "8659", "real_structure": "9659", "matched_3digits": "659", "predicted_position": "1-3", "real_position": "1-3"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 3}}, "314": {"period": 314, "predictions": ["1478", "7512", "6640", "6607", "4392", "6275", "6690", "7221", "9527", "5251", "5472", "5920", "4506", "4827", "2395", "8258", "1314", "8162", "0868", "4055", "1883", "0632", "7360", "8933", "5912", "6307", "1169", "3083", "9600", "7842"], "ground_truth": ["0746", "6978", "8638", "5666", "4566", "7449", "2217", "1170", "5257", "9738", "8700", "5577", "5535", "3757", "2363", "4755", "8721", "7562", "8434", "6613", "1051", "2826", "0551"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["221", "525", "055"], "partial_hits_3digit_details": [{"predicted_structure": "7221", "real_structure": "2217", "matched_3digits": "221", "predicted_position": "1-3", "real_position": "0-2"}, {"predicted_structure": "5251", "real_structure": "5257", "matched_3digits": "525", "predicted_position": "0-2", "real_position": "0-2"}, {"predicted_structure": "4055", "real_structure": "0551", "matched_3digits": "055", "predicted_position": "1-3", "real_position": "0-2"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 3}}, "315": {"period": 315, "predictions": ["3264", "1682", "3428", "9697", "3725", "4695", "0718", "3864", "6002", "0683", "4381", "7245", "6707", "9290", "0084", "6063", "3844", "8814", "2700", "9423", "6996", "3515", "9146", "0324", "3831", "9137", "2538", "1020", "2292", "2628"], "ground_truth": ["2512", "3428", "3662", "0550", "7914", "7615", "3165", "9095", "7162", "4018", "1606", "0294", "0222", "3898", "2338", "2452", "7676", "7881", "6601", "9041", "7321", "9088", "3910"], "evaluation_result": {"full_hits": 1, "full_hit_structures": ["3428"], "partial_hits_3digit": ["342", "245", "606", "881", "914"], "partial_hits_3digit_details": [{"predicted_structure": "3428", "real_structure": "3428", "matched_3digits": "342", "predicted_position": "0-2", "real_position": "0-2"}, {"predicted_structure": "7245", "real_structure": "2452", "matched_3digits": "245", "predicted_position": "1-3", "real_position": "0-2"}, {"predicted_structure": "6063", "real_structure": "1606", "matched_3digits": "606", "predicted_position": "0-2", "real_position": "1-3"}, {"predicted_structure": "8814", "real_structure": "7881", "matched_3digits": "881", "predicted_position": "0-2", "real_position": "1-3"}, {"predicted_structure": "9146", "real_structure": "7914", "matched_3digits": "914", "predicted_position": "0-2", "real_position": "1-3"}], "meets_requirement": false, "all_hit_structures": ["3428"], "total_hit_count": 1}, "summary": {"full_hits": 1, "meets_requirement": false, "partial_hits_count": 5}}, "316": {"period": 316, "predictions": ["0439", "7803", "7864", "6633", "6454", "5379", "6441", "7146", "6857", "7280", "6917", "4149", "2883", "6339", "7163", "1530", "5773", "4924", "2070", "1264", "9816", "5513", "0532", "5735", "2559", "2752", "9172", "8940", "4415", "9518"], "ground_truth": ["3364", "3011", "2035", "7526", "8992", "1176", "9235", "1277", "4435", "7008", "0504", "1007", "9063", "9377", "1100", "7320", "7300", "9058", "3208", "4496", "8799", "7333", "7228"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["752"], "partial_hits_3digit_details": [{"predicted_structure": "2752", "real_structure": "7526", "matched_3digits": "752", "predicted_position": "1-3", "real_position": "0-2"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 1}}, "317": {"period": 317, "predictions": ["6395", "9921", "3552", "7384", "1168", "7855", "6330", "7112", "6893", "9173", "4862", "4187", "5305", "8971", "0847", "9341", "7366", "0948", "0028", "5189", "5026", "4617", "3817", "0215", "8482", "3486", "9567", "2093", "4945", "8416"], "ground_truth": ["1869", "7946", "0811", "3007", "2098", "3633", "8965", "1608", "3500", "5091", "4270", "2746", "4950", "6754", "6801", "3155", "5788", "8098", "6021", "1751", "6308", "9745", "2057"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["633", "021", "209"], "partial_hits_3digit_details": [{"predicted_structure": "6330", "real_structure": "3633", "matched_3digits": "633", "predicted_position": "0-2", "real_position": "1-3"}, {"predicted_structure": "0215", "real_structure": "6021", "matched_3digits": "021", "predicted_position": "0-2", "real_position": "1-3"}, {"predicted_structure": "2093", "real_structure": "2098", "matched_3digits": "209", "predicted_position": "0-2", "real_position": "0-2"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 3}}, "318": {"period": 318, "predictions": ["3728", "6895", "3038", "7310", "0908", "7489", "1446", "9808", "9343", "5619", "3697", "4325", "9385", "9560", "0316", "3167", "9793", "7260", "9245", "7680", "6388", "4743", "5709", "4730", "8285", "0688", "8731", "6564", "2309", "8429"], "ground_truth": ["3431", "3282", "1311", "5728", "9967", "4245", "4357", "1366", "4198", "2980", "5542", "7106", "9112", "5153", "1623", "7171", "4909", "8747", "3312", "0691", "4085", "0928", "1248"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["728", "980", "343", "245"], "partial_hits_3digit_details": [{"predicted_structure": "3728", "real_structure": "5728", "matched_3digits": "728", "predicted_position": "1-3", "real_position": "1-3"}, {"predicted_structure": "9808", "real_structure": "2980", "matched_3digits": "980", "predicted_position": "0-2", "real_position": "1-3"}, {"predicted_structure": "9343", "real_structure": "3431", "matched_3digits": "343", "predicted_position": "1-3", "real_position": "0-2"}, {"predicted_structure": "9245", "real_structure": "4245", "matched_3digits": "245", "predicted_position": "1-3", "real_position": "1-3"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 4}}, "319": {"period": 319, "predictions": ["3965", "5473", "1264", "0574", "2113", "9175", "6021", "0143", "3566", "5490", "8845", "3910", "3085", "9309", "8909", "7517", "4077", "8254", "0168", "3670", "5471", "9894", "3529", "3859", "6325", "8797", "4153", "3628", "3464", "0260"], "ground_truth": ["9266", "7062", "9299", "5694", "6078", "8164", "0120", "7964", "7853", "8316", "7237", "0680", "9342", "5081", "2355", "6370", "2849", "3812", "4401", "3035", "4312", "1436", "2064"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["143"], "partial_hits_3digit_details": [{"predicted_structure": "0143", "real_structure": "1436", "matched_3digits": "143", "predicted_position": "1-3", "real_position": "0-2"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 1}}, "320": {"period": 320, "predictions": ["9403", "5861", "0376", "3394", "4270", "6648", "5466", "0398", "1724", "3104", "6493", "3471", "5213", "4971", "0804", "3107", "6828", "7143", "7997", "5610", "6411", "4655", "0609", "8643", "5344", "9372", "5860", "4396", "2449", "8360"], "ground_truth": ["8895", "3859", "8454", "7599", "3363", "9300", "9928", "7900", "3643", "2081", "2889", "9139", "6606", "5813", "0357", "4053", "0125", "9074", "7101", "0970", "2569", "7530", "2071"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["643"], "partial_hits_3digit_details": [{"predicted_structure": "8643", "real_structure": "3643", "matched_3digits": "643", "predicted_position": "1-3", "real_position": "1-3"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 1}}, "321": {"period": 321, "predictions": ["9118", "3486", "7177", "6851", "1040", "2733", "6864", "1952", "8163", "5834", "5896", "1905", "3995", "3062", "0918", "0273", "1608", "9352", "6074", "5506", "5284", "2940", "0724", "6842", "2601", "8447", "9681", "5476", "1279", "9230"], "ground_truth": ["2082", "4118", "6162", "3530", "1387", "2288", "5625", "9059", "3472", "6214", "8150", "8846", "1502", "5324", "6553", "2066", "6759", "2979", "0790", "3357", "8004", "4514", "3873"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["118", "905"], "partial_hits_3digit_details": [{"predicted_structure": "9118", "real_structure": "4118", "matched_3digits": "118", "predicted_position": "1-3", "real_position": "1-3"}, {"predicted_structure": "1905", "real_structure": "9059", "matched_3digits": "905", "predicted_position": "1-3", "real_position": "0-2"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 2}}, "322": {"period": 322, "predictions": ["7575", "1223", "5704", "5105", "9525", "6771", "0169", "5818", "6915", "8051", "5028", "4037", "3299", "2732", "3127", "0941", "3961", "2124", "3686", "2203", "8148", "1891", "6283", "0309", "0646", "6884", "5031", "4960", "6043", "8196"], "ground_truth": ["0170", "0253", "2832", "0421", "2036", "7857", "2204", "6962", "9519", "3864", "3822", "3730", "8049", "8442", "9444", "0734", "9042", "6422", "4740", "3947", "1328", "1552", "3621"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["203", "220", "283"], "partial_hits_3digit_details": [{"predicted_structure": "2203", "real_structure": "2036", "matched_3digits": "203", "predicted_position": "1-3", "real_position": "0-2"}, {"predicted_structure": "2203", "real_structure": "2204", "matched_3digits": "220", "predicted_position": "0-2", "real_position": "0-2"}, {"predicted_structure": "6283", "real_structure": "2832", "matched_3digits": "283", "predicted_position": "1-3", "real_position": "0-2"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 3}}, "323": {"period": 323, "predictions": ["4748", "4228", "1041", "3340", "5334", "8026", "1697", "9948", "7806", "5512", "4837", "5783", "4483", "4722", "2058", "9698", "1035", "6498", "2215", "5091", "7253", "5447", "6451", "3037", "8654", "9163", "5265", "6357", "7847", "5934"], "ground_truth": ["4249", "3869", "6820", "3100", "7942", "6509", "3343", "5800", "2584", "3922", "1045", "8627", "0574", "2963", "0475", "6025", "3736", "1361", "9653", "4267", "3425", "2755", "6839"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["104", "334", "334", "509"], "partial_hits_3digit_details": [{"predicted_structure": "1041", "real_structure": "1045", "matched_3digits": "104", "predicted_position": "0-2", "real_position": "0-2"}, {"predicted_structure": "3340", "real_structure": "3343", "matched_3digits": "334", "predicted_position": "0-2", "real_position": "0-2"}, {"predicted_structure": "5334", "real_structure": "3343", "matched_3digits": "334", "predicted_position": "1-3", "real_position": "0-2"}, {"predicted_structure": "5091", "real_structure": "6509", "matched_3digits": "509", "predicted_position": "0-2", "real_position": "1-3"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 4}}, "324": {"period": 324, "predictions": ["8362", "6973", "0152", "9330", "3491", "1695", "6269", "0198", "5545", "3960", "8910", "8403", "6512", "9316", "5813", "3097", "0509", "3190", "0590", "5290", "4723", "7952", "8954", "0852", "1683", "7949", "4356", "8590", "3604", "5420"], "ground_truth": ["1712", "8665", "2126", "7911", "2432", "1831", "1367", "0356", "5511", "7055", "9931", "4283", "5578", "6202", "1126", "0546", "7049", "8139", "9227", "6181", "9403", "9771", "3117"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["403", "931", "813", "356"], "partial_hits_3digit_details": [{"predicted_structure": "8403", "real_structure": "9403", "matched_3digits": "403", "predicted_position": "1-3", "real_position": "1-3"}, {"predicted_structure": "9316", "real_structure": "9931", "matched_3digits": "931", "predicted_position": "0-2", "real_position": "1-3"}, {"predicted_structure": "5813", "real_structure": "8139", "matched_3digits": "813", "predicted_position": "1-3", "real_position": "0-2"}, {"predicted_structure": "4356", "real_structure": "0356", "matched_3digits": "356", "predicted_position": "1-3", "real_position": "1-3"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 4}}, "325": {"period": 325, "predictions": ["8107", "4437", "9497", "1006", "5062", "6719", "0729", "0567", "4484", "3464", "5225", "2057", "0835", "6346", "6203", "5261", "7735", "4340", "8105", "9133", "8420", "5381", "6248", "1342", "2452", "0550", "1765", "3788", "2850", "7359"], "ground_truth": ["0870", "5304", "6790", "0307", "4794", "4362", "2461", "1231", "8903", "5049", "4288", "1177", "5128", "7272", "3416", "1776", "9938", "0854", "3043", "8920", "8824", "7442", "0262"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": [], "partial_hits_3digit_details": [], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 0}}, "326": {"period": 326, "predictions": ["8351", "1724", "4752", "9047", "3272", "3867", "8890", "3022", "9934", "7808", "7136", "5163", "8075", "4983", "4285", "5425", "0954", "1974", "4825", "5743", "8413", "7329", "3597", "9537", "1873", "4818", "0493", "5769", "5135", "3093"], "ground_truth": ["7900", "3179", "9984", "2576", "1165", "8670", "3722", "7397", "6400", "9976", "8898", "0869", "1943", "3317", "1847", "6659", "2821", "1749", "7699", "9485", "7702", "6403", "6959"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["867", "889", "576", "769"], "partial_hits_3digit_details": [{"predicted_structure": "3867", "real_structure": "8670", "matched_3digits": "867", "predicted_position": "1-3", "real_position": "0-2"}, {"predicted_structure": "8890", "real_structure": "8898", "matched_3digits": "889", "predicted_position": "0-2", "real_position": "0-2"}, {"predicted_structure": "5769", "real_structure": "2576", "matched_3digits": "576", "predicted_position": "0-2", "real_position": "1-3"}, {"predicted_structure": "5769", "real_structure": "7699", "matched_3digits": "769", "predicted_position": "1-3", "real_position": "0-2"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 4}}, "327": {"period": 327, "predictions": ["5195", "7915", "9253", "7384", "2344", "2853", "4659", "1853", "1262", "0281", "9156", "3026", "9837", "9000", "1599", "1084", "5614", "9851", "7701", "7451", "7215", "6684", "4673", "5849", "8472", "5339", "3374", "4638", "7138", "4296"], "ground_truth": ["5439", "0063", "8192", "8715", "6250", "0685", "8071", "3835", "6677", "3644", "1575", "6658", "2991", "0304", "5381", "9251", "7068", "0694", "4134", "4867", "6567", "2785", "1271"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["925"], "partial_hits_3digit_details": [{"predicted_structure": "9253", "real_structure": "9251", "matched_3digits": "925", "predicted_position": "0-2", "real_position": "0-2"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 1}}, "328": {"period": 328, "predictions": ["1980", "0184", "8504", "7588", "8196", "4498", "4131", "4480", "8456", "2744", "9826", "7904", "6081", "2417", "0663", "5540", "2617", "6014", "6085", "3197", "5555", "8586", "4912", "4240", "4635", "1370", "2778", "7423", "4023", "9653"], "ground_truth": ["1498", "4149", "9290", "7059", "9483", "5517", "6483", "1492", "5852", "8399", "2057", "7114", "4688", "2078", "6848", "1785", "9305", "0648", "1125", "1553", "5195", "3317", "4287"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["498"], "partial_hits_3digit_details": [{"predicted_structure": "4498", "real_structure": "1498", "matched_3digits": "498", "predicted_position": "1-3", "real_position": "1-3"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 1}}, "329": {"period": 329, "predictions": ["3546", "6489", "9770", "9042", "0892", "4046", "3114", "9395", "6843", "6062", "6219", "8507", "2726", "4569", "0851", "6552", "8597", "9534", "2737", "4790", "3709", "5334", "0834", "2340", "2702", "7451", "4570", "6989", "8048", "2344"], "ground_truth": ["4966", "9135", "0399", "3969", "0123", "7098", "3313", "7951", "2573", "7148", "8595", "0183", "5752", "3331", "1071", "4913", "1416", "8871", "1429", "1976", "7144", "4161", "0885"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["859", "709"], "partial_hits_3digit_details": [{"predicted_structure": "8597", "real_structure": "8595", "matched_3digits": "859", "predicted_position": "0-2", "real_position": "0-2"}, {"predicted_structure": "3709", "real_structure": "7098", "matched_3digits": "709", "predicted_position": "1-3", "real_position": "0-2"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 2}}, "330": {"period": 330, "predictions": ["8596", "5557", "1294", "0503", "2455", "6774", "7854", "9017", "6004", "2477", "5947", "9881", "6536", "8040", "2908", "6871", "2567", "1161", "7904", "7682", "7618", "0583", "6488", "4758", "3547", "4755", "6371", "6053", "4136", "8160"], "ground_truth": ["1531", "8376", "9836", "2943", "5028", "4436", "2549", "3044", "9244", "2799", "3085", "1710", "8608", "4841", "9787", "1722", "2193", "6527", "6181", "3696", "8691", "6203", "8804"], "evaluation_result": {"full_hits": 0, "full_hit_structures": [], "partial_hits_3digit": ["294", "804", "618"], "partial_hits_3digit_details": [{"predicted_structure": "1294", "real_structure": "2943", "matched_3digits": "294", "predicted_position": "1-3", "real_position": "0-2"}, {"predicted_structure": "8040", "real_structure": "8804", "matched_3digits": "804", "predicted_position": "0-2", "real_position": "1-3"}, {"predicted_structure": "7618", "real_structure": "6181", "matched_3digits": "618", "predicted_position": "1-3", "real_position": "0-2"}], "meets_requirement": false, "all_hit_structures": [], "total_hit_count": 0}, "summary": {"full_hits": 0, "meets_requirement": false, "partial_hits_count": 3}}}, "test_log": [{"period": 301, "full_hits": 0, "partial_hits_count": 1, "partial_hits": ["604"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 302, "full_hits": 0, "partial_hits_count": 2, "partial_hits": ["645", "711"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 303, "full_hits": 0, "partial_hits_count": 2, "partial_hits": ["051", "344"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 304, "full_hits": 0, "partial_hits_count": 2, "partial_hits": ["980", "291"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 305, "full_hits": 0, "partial_hits_count": 2, "partial_hits": ["633", "307"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 306, "full_hits": 0, "partial_hits_count": 4, "partial_hits": ["707", "897", "736", "518"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 307, "full_hits": 0, "partial_hits_count": 1, "partial_hits": ["638"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 308, "full_hits": 0, "partial_hits_count": 1, "partial_hits": ["591"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 309, "full_hits": 0, "partial_hits_count": 2, "partial_hits": ["484", "305"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 310, "full_hits": 0, "partial_hits_count": 6, "partial_hits": ["351", "076", "328", "064", "138", "935"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 311, "full_hits": 0, "partial_hits_count": 4, "partial_hits": ["976", "976", "341", "139"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 312, "full_hits": 0, "partial_hits_count": 2, "partial_hits": ["198", "970"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 313, "full_hits": 0, "partial_hits_count": 3, "partial_hits": ["446", "502", "659"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 314, "full_hits": 0, "partial_hits_count": 3, "partial_hits": ["221", "525", "055"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 315, "full_hits": 1, "partial_hits_count": 5, "partial_hits": ["342", "245", "606", "881", "914"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 316, "full_hits": 0, "partial_hits_count": 1, "partial_hits": ["752"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 317, "full_hits": 0, "partial_hits_count": 3, "partial_hits": ["633", "021", "209"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 318, "full_hits": 0, "partial_hits_count": 4, "partial_hits": ["728", "980", "343", "245"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 319, "full_hits": 0, "partial_hits_count": 1, "partial_hits": ["143"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 320, "full_hits": 0, "partial_hits_count": 1, "partial_hits": ["643"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:30"}, {"period": 321, "full_hits": 0, "partial_hits_count": 2, "partial_hits": ["118", "905"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:31"}, {"period": 322, "full_hits": 0, "partial_hits_count": 3, "partial_hits": ["203", "220", "283"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:31"}, {"period": 323, "full_hits": 0, "partial_hits_count": 4, "partial_hits": ["104", "334", "334", "509"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:31"}, {"period": 324, "full_hits": 0, "partial_hits_count": 4, "partial_hits": ["403", "931", "813", "356"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:31"}, {"period": 325, "full_hits": 0, "partial_hits_count": 0, "partial_hits": [], "meets_requirement": false, "timestamp": "2025-06-09 12:49:31"}, {"period": 326, "full_hits": 0, "partial_hits_count": 4, "partial_hits": ["867", "889", "576", "769"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:31"}, {"period": 327, "full_hits": 0, "partial_hits_count": 1, "partial_hits": ["925"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:31"}, {"period": 328, "full_hits": 0, "partial_hits_count": 1, "partial_hits": ["498"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:31"}, {"period": 329, "full_hits": 0, "partial_hits_count": 2, "partial_hits": ["859", "709"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:31"}, {"period": 330, "full_hits": 0, "partial_hits_count": 3, "partial_hits": ["294", "804", "618"], "meets_requirement": false, "timestamp": "2025-06-09 12:49:31"}]}}