{"model_type": "multi_position", "version": "1.0.0", "created_date": "2025-01-07T00:00:00", "training_samples": 10000, "features": {"position_weights": "optimized", "bias_correction": true, "adaptive_weights": true, "position_correlation": true}, "description": "多位置预测模型，支持位置优化功能", "optimization_features": {"position_adaptive_weights": {"enabled": true, "description": "基于历史准确率的自适应权重调整"}, "bias_correction": {"enabled": true, "description": "基于历史偏差的校正算法"}, "position_correlation": {"enabled": true, "description": "位置间相关性约束机制"}}, "performance_improvements": {"partial_hit_optimization": "针对3位命中但第4位偏差的情况进行优化", "bias_patterns_detected": {"个位": "+2到+4的正偏差", "百位": "±2的双向偏差"}}, "configuration": {"weight_type": "optimized", "scoring_strategy": "weighted_average", "enable_bias_correction": true, "enable_adaptive_weights": true, "enable_position_correlation": true}}