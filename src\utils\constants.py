"""
常量定义模块：集中管理系统中使用的全局常量。

本模块的唯一职责是定义系统中各模块共享使用的固定值常量，
为各模块提供标准变量，避免魔法数字、硬编码与重复定义。
不执行任何运算、不控制流程、不写入数据，仅提供常量引用。
"""

import logging
import os
from typing import Any, Dict, List

import yaml

# 获取日志器
logger = logging.getLogger(__name__)

# 数值范围
DIGIT_RANGE = list(range(10))  # 0-9

# 位置信息
POSITIONS = ["k", "b", "s", "g"]  # 千位、百位、十位、个位代号
POSITION_NAMES = {"k": "千位", "b": "百位", "s": "十位", "g": "个位"}

# 奖项数量
TOTAL_PRIZES = 23

# 策略路径（用于标签判断）
STRATEGY_PATHS = [
    "爆破",
    "冷修复",
    "高频周期",
    "冷热混合",
    "稳定性",
    "趋势跟踪",
    "多样化处理",
    "均值回归",
    "极值",
    "组合模式",
]

# 策略名称映射
STRATEGY_NAMES = {
    "爆破": "高频爆破策略",
    "冷修复": "冷号修复策略",
    "高频周期": "周期规律策略",
    "冷热混合": "冷热混合策略",
    "稳定性": "稳定性策略",
    "趋势跟踪": "趋势跟踪策略",
    "多样化处理": "多样化处理策略",
    "均值回归": "均值回归策略",
    "极值": "极值策略",
    "组合模式": "组合模式策略",
}


# 尝试从配置文件加载策略默认权重
def _load_strategy_weights_from_config() -> Dict[str, float]:
    """从配置文件加载策略权重"""
    config_path = os.path.join(
        os.path.dirname(os.path.dirname(__file__)), "config.yaml"
    )
    if os.path.exists(config_path):
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
                if config and "strategy_paths" in config:
                    # 过滤出策略权重（排除其他配置项）
                    weights = {
                        k: v
                        for k, v in config["strategy_paths"].items()
                        if k in STRATEGY_PATHS
                    }
                    if weights:
                        return weights
        except Exception:  # pylint: disable=broad-exception-caught
            pass
    # 如果无法从配置加载，使用4个有效策略的权重分配
    return {
        "多样化处理": 0.35,  # 182期表现最佳策略
        "高频周期": 0.30,  # 182期表现最佳策略
        "均值回归": 0.20,  # 获得更多资源
        "极值": 0.13,  # 稳定有效策略
        "组合模式": 0.02,  # 最稳定策略
        "冷修复": 0.00,  # 关闭无效策略
        "冷热混合": 0.00,  # 关闭无效策略
        "爆破": 0.00,  # 关闭无效策略
        "稳定性": 0.00,  # 关闭无效策略
        "趋势跟踪": 0.00,  # 关闭无效策略
    }


# 策略默认权重
STRATEGY_DEFAULT_WEIGHT = _load_strategy_weights_from_config()

# 默认配置
MAX_ITERATIONS = 5
TOP_N_OUTPUT = 30

# 文件路径
DEFAULT_CONFIG_PATH = "src/config.yaml"
DEFAULT_MEMORY_PATH = "data/memory/brain_memory.json"
DEFAULT_MODEL_DIR = "models"
DEFAULT_MODEL_PATH = os.path.join(
    DEFAULT_MODEL_DIR, "xgboost_model.json"
)  # 统一使用xgboost_model.json
DEFAULT_LOG_PATH = "logs/system.log"

# 日志级别
LOG_LEVEL_DEBUG = "DEBUG"
LOG_LEVEL_INFO = "INFO"
LOG_LEVEL_WARNING = "WARNING"
LOG_LEVEL_ERROR = "ERROR"
LOG_LEVEL_CRITICAL = "CRITICAL"
DEFAULT_LOG_LEVEL = LOG_LEVEL_INFO

# 错误代码
ERROR_CODES = {
    # 数据相关错误 (E001-E099)
    "E001": "数据为空",
    "E002": "数据格式错误",
    "E003": "数据加载失败",
    "E004": "数据拆分失败",
    "E005": "数据保存失败",
    "E006": "数据转换失败",
    "E007": "数据索引错误",
    "E008": "数据列缺失",
    "E009": "数据类型错误",
    # 特征相关错误 (E100-E199)
    "E100": "特征生成失败",
    "E101": "特征矩阵为空",
    "E102": "特征不存在",
    "E103": "特征提取失败",
    "E104": "特征转换失败",
    "E105": "特征保存失败",
    "E106": "特征加载失败",
    "E107": "特征维度不匹配",
    "E108": "特征类型错误",
    "E109": "特征名称错误",
    # 模型相关错误 (E200-E299)
    "E200": "模型训练失败",
    "E201": "模型预测失败",
    "E202": "模型保存失败",
    "E203": "模型加载失败",
    "E204": "模型文件不存在",
    "E205": "模型参数错误",
    "E206": "模型评估失败",
    "E207": "模型初始化失败",
    "E208": "模型类型错误",
    "E209": "模型版本不兼容",
    # 规则相关错误 (E300-E399)
    "E300": "规则评分失败",
    "E301": "规则权重错误",
    "E302": "规则应用失败",
    "E303": "规则定义错误",
    "E304": "规则加载失败",
    "E305": "规则保存失败",
    "E306": "规则验证失败",
    "E307": "规则执行超时",
    "E308": "规则冲突",
    "E309": "规则不存在",
    # 结构相关错误 (E400-E499)
    "E400": "结构生成失败",
    "E401": "结构数量不足",
    "E402": "结构格式错误",
    "E403": "结构转换失败",
    "E404": "结构保存失败",
    "E405": "结构加载失败",
    "E406": "结构验证失败",
    "E407": "结构重复",
    "E408": "结构不存在",
    "E409": "结构类型错误",
    # 系统相关错误 (E500-E599)
    "E500": "系统初始化失败",
    "E501": "系统配置错误",
    "E502": "系统资源不足",
    "E503": "系统权限错误",
    "E504": "系统文件错误",
    "E505": "系统网络错误",
    "E506": "系统超时",
    "E507": "系统依赖错误",
    "E508": "系统状态错误",
    "E509": "系统未知错误",
    # IO相关错误 (E600-E699)
    "E600": "文件不存在",
    "E601": "文件读取失败",
    "E602": "文件写入失败",
    "E603": "文件格式错误",
    "E604": "文件权限错误",
    "E605": "文件路径错误",
    "E606": "文件创建失败",
    "E607": "文件删除失败",
    "E608": "文件移动失败",
    "E609": "文件复制失败",
    # 预测相关错误 (E700-E799)
    "E700": "预测流程失败",
    "E701": "预测结果为空",
    "E702": "预测参数错误",
    "E703": "预测超时",
    "E704": "预测资源不足",
    "E705": "预测初始化失败",
    "E706": "预测验证失败",
    "E707": "预测保存失败",
    "E708": "预测加载失败",
    "E709": "预测类型错误",
}

# 特征类型
FEATURE_TYPES = {
    "COLD": "冷号",
    "HOT": "热号",
    "CYCLE": "周期",
    "MARKOV": "马尔可夫",
    "FREQ": "频率",
    "MEAN": "平均值",
    "STABLE": "稳定",
}


# 尝试从配置文件加载XGBoost参数
def _load_xgboost_params_from_config() -> Dict[str, Any]:
    """从配置文件加载XGBoost参数"""
    config_path = os.path.join(
        os.path.dirname(os.path.dirname(__file__)), "config.yaml"
    )
    if os.path.exists(config_path):
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
                if config and "model" in config and "xgboost_params" in config["model"]:
                    xgb_params = config["model"]["xgboost_params"]
                    # 转换参数名称以兼容不同的XGBoost API
                    unified_params = {}
                    for key, value in xgb_params.items():
                        if key == "learning_rate":
                            # 同时设置learning_rate和eta，确保兼容性
                            unified_params["learning_rate"] = value
                            unified_params["eta"] = value
                        elif key == "random_state":
                            # 同时设置random_state和seed，确保兼容性
                            unified_params["random_state"] = value
                            unified_params["seed"] = value
                        else:
                            unified_params[key] = value
                    return unified_params
        except Exception as e:  # pylint: disable=broad-exception-caught
            logger.warning("从配置文件加载XGBoost参数失败: %s", e)

    # 如果无法从配置加载，使用激进优化配置作为默认值
    return {
        "objective": "binary:logistic",
        "eval_metric": "logloss",
        "max_depth": 8,  # 🔧 激进优化：降低树深度，提高泛化能力
        "learning_rate": 0.1,  # 🔧 激进优化：大幅提高学习率，加快收敛
        "eta": 0.1,  # XGBoost原生参数名
        "n_estimators": 2000,  # 🔧 激进优化：大幅增加树数量，提高模型容量
        "scale_pos_weight": 3.0,  # 🔧 基于数据修正：使用合理的正样本权重，避免过度偏向
        "subsample": 0.8,  # 🔧 激进优化：降低采样率，增加模型多样性
        "colsample_bytree": 0.8,  # 🔧 激进优化：降低特征采样率，增加模型多样性
        "min_child_weight": 2.0,  # 🔧 激进优化：增加约束，防止过拟合稀疏正样本
        "gamma": 0.3,  # 🔧 激进优化：大幅增加分裂代价，减少过拟合
        "reg_alpha": 0.05,  # 🔧 激进优化：大幅增加L1正则化，特征选择
        "reg_lambda": 0.02,  # 🔧 激进优化：适度增加L2正则化
        "random_state": 42,  # 随机种子
        "seed": 42,  # XGBoost原生参数名
    }


# 统一的XGBoost模型参数（从配置文件加载）
DEFAULT_MODEL_PARAMS = _load_xgboost_params_from_config()

# MLP模型路径 - 使用Keras原生格式避免HDF5弃用警告
DEFAULT_MLP_MODEL_PATH = os.path.join(DEFAULT_MODEL_DIR, "mlp_model.keras")

# 评分权重 - 平衡配置：规则×0.3, 模型×0.7
DEFAULT_SCORE_WEIGHTS = {"rule": 0.3, "model": 0.7}

# 🔧 第一阶段优化：统一三模型评分权重配置
DEFAULT_SCORE_WEIGHTS_THREE = {"rule": 0.15, "xgb": 0.50, "mlp": 0.35}


class Constants:
    """常量管理类，提供系统常量的统一访问接口"""

    def __init__(self):
        """初始化常量管理器"""

    def get_feature_names(self) -> List[str]:
        """
        获取特征名称列表

        返回:
            List[str]: 特征名称列表
        """
        feature_names = []

        # 基础特征
        for pos in POSITIONS:
            for digit in DIGIT_RANGE:
                feature_names.extend(
                    [
                        f"cold_{pos}_{digit}",
                        f"hot_{pos}_{digit}",
                        f"freq_{pos}_{digit}",
                        f"cycle_{pos}_{digit}",
                        f"markov_{pos}_{digit}",
                    ]
                )

        # 统计特征
        for pos in POSITIONS:
            feature_names.extend(
                [
                    f"mean_{pos}",
                    f"std_{pos}",
                    f"ma5_{pos}",
                    f"ma10_{pos}",
                    f"trend_{pos}",
                ]
            )

        # 组合特征
        position_pairs = [("k", "g"), ("k", "b"), ("b", "s"), ("s", "g")]
        for pos1, pos2 in position_pairs:
            for num1 in DIGIT_RANGE:
                for num2 in DIGIT_RANGE:
                    feature_names.append(f"combo_{pos1}_{pos2}_{num1}{num2}")

        return feature_names

    def get_strategy_names(self) -> List[str]:
        """
        获取策略名称列表

        返回:
            List[str]: 策略名称列表
        """
        return STRATEGY_PATHS.copy()

    def get_default_values(self) -> Dict[str, Any]:
        """
        获取默认值字典

        返回:
            Dict[str, Any]: 默认值字典
        """
        return {
            "strategy_weights": STRATEGY_DEFAULT_WEIGHT.copy(),
            "score_weights": DEFAULT_SCORE_WEIGHTS.copy(),
            "score_weights_three": DEFAULT_SCORE_WEIGHTS_THREE.copy(),
            "model_params": DEFAULT_MODEL_PARAMS.copy(),
            "max_iterations": MAX_ITERATIONS,
            "top_n_output": TOP_N_OUTPUT,
            "total_prizes": TOTAL_PRIZES,
            "positions": POSITIONS.copy(),
            "position_names": POSITION_NAMES.copy(),
            "feature_types": FEATURE_TYPES.copy(),
            "log_level": DEFAULT_LOG_LEVEL,
        }
