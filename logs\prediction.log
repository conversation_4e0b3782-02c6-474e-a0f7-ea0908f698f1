2025-05-31 17:15:10,710 - __main__ - INFO - 日志系统初始化完成，级别: INFO，日志文件: D:\可以命中3位\logs\prediction.log
2025-05-31 17:15:11,059 - src.config - INFO - 成功加载配置文件: D:\可以命中3位\src\config\..\config.yaml
2025-05-31 17:15:11,060 - src.config - INFO - 扁平化scoring配置: ['model_weight', 'normalize_scores', 'rule_weight', 'score_clip_range', 'score_weights', 'score_weights_three']
2025-05-31 17:15:11,060 - src.config - INFO - 扁平化system配置: ['candidate_count', 'retrain_interval']
2025-05-31 17:15:11,060 - src.config - INFO - 扁平化strategy_weights配置: ['冷修复', '冷热混合', '爆破', '稳定性', '高频周期']
2025-05-31 17:15:11,061 - src.config - INFO - 扁平化strategy_selector配置: ['boost_rate', 'decay_rate', 'diversity_factor', 'enable_diversity', 'exploration_factor', 'max_cooldown_allowed', 'max_unused_time', 'max_weight', 'min_weight']
2025-05-31 17:15:11,063 - src.config - INFO - 扁平化feedback配置: ['consider_partial_hits', 'partial_hit_weight']
2025-05-31 17:15:11,064 - src.config - INFO - 扁平化selection配置: ['diversity_enforce', 'filter_illegal', 'min_strategy_representation', 'top_n']
2025-05-31 17:15:11,064 - src.config - INFO - 使用环境变量 DATA_PATH 覆盖配置 data_path: 历史数据.xlsx
2025-05-31 17:15:11,064 - src.config - INFO - 使用环境变量 TRAIN_RANGE 覆盖配置 train_range: (1, 150)
