#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最终修复验证脚本

验证系统是否已经完全修复了原来的无限循环错误
"""

import os
import sys
import numpy as np
import polars as pl

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))

from src.left_brain.model_scorer import predict_model_scores_polars
from src.utils.logger import get_logger

# 配置日志
logger = get_logger("final_fix_test")


def test_system_stability():
    """测试系统稳定性，确保不会出现无限循环"""
    logger.info("=" * 60)
    logger.info("🚀 最终修复验证：系统稳定性测试")
    logger.info("=" * 60)

    # 创建测试数据
    test_structures = ["1234", "5678", "9012", "3456", "7890"]

    # 创建测试特征DataFrame
    feature_data = []
    for structure in test_structures:
        features = {f"feature_{i}": np.random.rand() for i in range(10)}
        feature_data.append({"structure": structure, "features": features})

    feature_df = pl.DataFrame(feature_data)

    logger.info(f"测试结构数量: {len(test_structures)}")
    logger.info(f"特征DataFrame形状: {feature_df.shape}")

    # 测试1: 使用多位置模型路径（应该自动回退）
    logger.info("\n📋 测试1: 多位置模型路径回退")
    try:
        model_path = "models/multi_position"
        scores = predict_model_scores_polars(test_structures, feature_df, model_path)

        if scores and len(scores) == len(test_structures):
            logger.info("✅ 多位置模型路径回退成功")
            logger.info(f"   获得 {len(scores)} 个评分结果")
            return True
        else:
            logger.error("❌ 评分结果数量不正确")
            return False

    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False


def test_normal_model_path():
    """测试正常模型路径"""
    logger.info("\n📋 测试2: 正常模型路径")

    # 创建简单测试数据
    test_structures = ["1111", "2222"]
    feature_data = []
    for structure in test_structures:
        features = {f"feature_{i}": np.random.rand() for i in range(5)}
        feature_data.append({"structure": structure, "features": features})

    feature_df = pl.DataFrame(feature_data)

    try:
        # 使用备用模型路径
        from src.left_brain.model_scorer import _get_backup_xgboost_model

        backup_model = _get_backup_xgboost_model()

        if backup_model:
            scores = predict_model_scores_polars(
                test_structures, feature_df, backup_model
            )

            if scores and len(scores) == len(test_structures):
                logger.info("✅ 正常模型路径测试成功")
                logger.info(f"   使用模型: {backup_model}")
                logger.info(f"   获得 {len(scores)} 个评分结果")
                return True
            else:
                logger.error("❌ 评分结果数量不正确")
                return False
        else:
            logger.warning("⚠️ 未找到备用模型，跳过测试")
            return True

    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("🔧 开始最终修复验证")

    # 测试系统稳定性
    stability_test = test_system_stability()

    # 测试正常模型路径
    normal_test = test_normal_model_path()

    # 汇总结果
    logger.info("=" * 60)
    logger.info("📊 最终测试结果")
    logger.info("=" * 60)

    if stability_test and normal_test:
        logger.info("🎉 所有测试通过！系统修复成功")
        logger.info("✅ 无限循环错误已修复")
        logger.info("✅ 备用模型选择机制正常工作")
        logger.info("✅ 系统稳定性良好")
        logger.info("=" * 60)
        logger.info("🚀 系统现在可以正常运行了！")
        return True
    else:
        logger.error("❌ 部分测试失败")
        logger.error(f"   稳定性测试: {'✅' if stability_test else '❌'}")
        logger.error(f"   正常路径测试: {'✅' if normal_test else '❌'}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
