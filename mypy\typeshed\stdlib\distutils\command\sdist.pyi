from _typeshed import Incomplete, Unused
from collections.abc import Callable
from typing import Any, ClassVar

from ..cmd import Command

def show_formats() -> None: ...

class sdist(Command):
    description: str
    def checking_metadata(self): ...
    user_options: ClassVar[list[tuple[str, str | None, str]]]
    boolean_options: ClassVar[list[str]]
    help_options: ClassVar[list[tuple[str, str | None, str, Callable[[], Unused]]]]
    negative_opt: ClassVar[dict[str, str]]
    # Any to work around variance issues
    sub_commands: ClassVar[list[tuple[str, Callable[[Any], bool] | None]]]
    READMES: ClassVar[tuple[str, ...]]
    template: Incomplete
    manifest: Incomplete
    use_defaults: int
    prune: int
    manifest_only: int
    force_manifest: int
    formats: Incomplete
    keep_temp: int
    dist_dir: Incomplete
    archive_files: Incomplete
    metadata_check: int
    owner: Incomplete
    group: Incomplete
    def initialize_options(self) -> None: ...
    def finalize_options(self) -> None: ...
    filelist: Incomplete
    def run(self) -> None: ...
    def check_metadata(self) -> None: ...
    def get_file_list(self) -> None: ...
    def add_defaults(self) -> None: ...
    def read_template(self) -> None: ...
    def prune_file_list(self) -> None: ...
    def write_manifest(self) -> None: ...
    def read_manifest(self) -> None: ...
    def make_release_tree(self, base_dir, files) -> None: ...
    def make_distribution(self) -> None: ...
    def get_archive_files(self): ...
