diversifier:
  crossover_rate: 0.3
  mutation_rate: 0.4
  position_swap_rate: 0.3
features:
  normalize_features: true
  use_cross_position: true
  use_markov: true
  window_10: 10
  window_30: 30
  window_5: 5
feedback:
  consider_partial_hits: false
  partial_hit_weight: 0.0
iteration:
  early_stop_on_hit: true
  feedback_mode: path_tagged
  feedback_trigger_on_zero: true
  max_rounds: 5
logging:
  level: INFO
  log_format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  log_to_file: true
# 🔧 第一阶段优化：MLP参数优化
mlp:
  mlp_params:
    batch_size: 64  # 增加批量大小，提高训练稳定性
    dropout_rate: 0.3  # 减少dropout，保留更多信息
    early_stopping_patience: 20  # 增加耐心值
    epochs: 150  # 增加训练轮数
    hidden_layers:
    - 512  # 增加第一层神经元
    - 256  # 增加第二层神经元
    - 128  # 增加第三层神经元
    - 64   # 添加第四层
    l1_reg: 0.001  # 添加L1正则化
    l2_reg: 0.01   # 减少L2正则化
    learning_rate: 0.0005  # 降低学习率
    reduce_lr_patience: 8  # 增加学习率衰减耐心值
    validation_split: 0.25  # 增加验证集比例
  model_path: models/mlp_model.keras
  train_mlp: true
  use_mlp: true
# 🔧 第一阶段优化：XGBoost参数优化
model:
  path: models/xgboost_model.json
  threshold: 0.5
  type: xgboost
  use_model: true
  xgboost_params:
    colsample_bytree: 0.85  # 减少特征采样，防止过拟合
    eval_metric: logloss
    gamma: 0.05  # 降低gamma，允许更多分裂
    learning_rate: 0.02  # 降低学习率，提高稳定性
    max_depth: 6  # 减少深度，防止过拟合
    min_child_weight: 3.0  # 增加最小子节点权重
    n_estimators: 2500  # 增加树的数量
    objective: binary:logistic
    random_state: 42
    reg_alpha: 0.01  # 减少L1正则化
    reg_lambda: 0.05  # 增加L2正则化
    scale_pos_weight: 5.0  # 调整正负样本权重平衡
    subsample: 0.85  # 减少样本采样，提高泛化
paths:
  data_path: data/lottery_history.xlsx
  # 🔧 修复：移除硬编码，使用paths.py中的FEEDBACK_DIR
  # feedback_save_path: data/feedback  # 已移除硬编码
  log_dir: logs
  memory_path: data/memory
  model_dir: models
  result_save_path: data/results
rule_weights:
  # 主要冷号特征权重 - 提升权重增加差异性
  cold_b: 0.4
  cold_g: 0.4
  cold_k: 0.4
  cold_t: 0.4
  # 冷号缺失期特征
  cold_gap30_b: 0.05
  cold_gap30_g: 0.05
  cold_gap30_k: 0.05
  cold_gap30_t: 0.05
  cross_bg: 0.03
  cross_bt: 0.03
  cross_kb: 0.03
  cross_kg: 0.03
  cross_kt: 0.03
  cross_tg: 0.03
  dist20_b_1: 0.02
  dist20_b_6: 0.02
  dist20_g_3: 0.02
  dist20_g_8: 0.02
  dist20_k_0: 0.02
  dist20_k_5: 0.02
  dist20_t_2: 0.02
  dist20_t_7: 0.02
  # 频率特征权重 - 提升权重
  freq5_b: 0.08
  freq5_g: 0.08
  freq5_k: 0.08
  freq5_t: 0.08
  # 热号特征权重 - 增加负权重影响
  hot_b: -0.25
  hot_g: -0.25
  hot_k: -0.25
  hot_t: -0.25
  ma_b_3: 0.02
  ma_b_5: 0.02
  ma_g_3: 0.02
  ma_g_5: 0.02
  ma_k_3: 0.02
  ma_k_5: 0.02
  ma_t_3: 0.02
  ma_t_5: 0.02
  markov1_b_1to0: 0.021
  markov1_b_1to1: 0.021
  markov1_b_1to2: 0.021
  markov1_b_2to0: 0.021
  markov1_b_2to1: 0.021
  markov1_b_2to2: 0.021
  markov1_g_1to0: 0.021
  markov1_g_1to1: 0.021
  markov1_g_1to2: 0.021
  markov1_g_2to0: 0.021
  markov1_g_2to1: 0.021
  markov1_g_2to2: 0.021
  markov1_k_1to0: 0.021
  markov1_k_1to1: 0.021
  markov1_k_1to2: 0.021
  markov1_k_2to0: 0.021
  markov1_k_2to1: 0.021
  markov1_k_2to2: 0.021
  markov1_t_1to0: 0.021
  markov1_t_1to1: 0.021
  markov1_t_1to2: 0.021
  markov1_t_2to0: 0.021
  markov1_t_2to1: 0.021
  markov1_t_2to2: 0.021
  markov2_b_0_1to0: 0.009
  markov2_b_0_1to1: 0.009
  markov2_b_0_1to2: 0.009
  markov2_b_0_2to0: 0.009
  markov2_b_0_2to1: 0.009
  markov2_b_0_2to2: 0.009
  markov2_g_0_1to0: 0.009
  markov2_g_0_1to1: 0.009
  markov2_g_0_1to2: 0.009
  markov2_g_0_2to0: 0.009
  markov2_g_0_2to1: 0.009
  markov2_g_0_2to2: 0.009
  markov2_k_0_1to0: 0.009
  markov2_k_0_1to1: 0.009
  markov2_k_0_1to2: 0.009
  markov2_k_0_2to0: 0.009
  markov2_k_0_2to1: 0.009
  markov2_k_0_2to2: 0.009
  markov2_t_0_1to0: 0.009
  markov2_t_0_1to1: 0.009
  markov2_t_0_1to2: 0.009
  markov2_t_0_2to0: 0.009
  markov2_t_0_2to1: 0.009
  markov2_t_0_2to2: 0.009
  # 周期性特征权重 - 提升权重
  period_b: 0.1
  period_g: 0.1
  period_k: 0.1
  period_t: 0.1
  trans_b_0to0: 0.01
  trans_b_1to1: 0.01
  trans_b_2to2: 0.01
  trans_b_3to3: 0.01
  trans_b_4to4: 0.01
  trans_b_5to5: 0.01
  trans_b_6to6: 0.01
  trans_b_7to7: 0.01
  trans_b_8to8: 0.01
  trans_b_9to9: 0.01
  trans_g_0to0: 0.01
  trans_g_1to1: 0.01
  trans_g_2to2: 0.01
  trans_g_3to3: 0.01
  trans_g_4to4: 0.01
  trans_g_5to5: 0.01
  trans_g_6to6: 0.01
  trans_g_7to7: 0.01
  trans_g_8to8: 0.01
  trans_g_9to9: 0.01
  trans_k_0to0: 0.01
  trans_k_1to1: 0.01
  trans_k_2to2: 0.01
  trans_k_3to3: 0.01
  trans_k_4to4: 0.01
  trans_k_5to5: 0.01
  trans_k_6to6: 0.01
  trans_k_7to7: 0.01
  trans_k_8to8: 0.01
  trans_k_9to9: 0.01
  trans_t_0to0: 0.01
  trans_t_1to1: 0.01
  trans_t_2to2: 0.01
  trans_t_3to3: 0.01
  trans_t_4to4: 0.01
  trans_t_5to5: 0.01
  trans_t_6to6: 0.01
  trans_t_7to7: 0.01
  trans_t_8to8: 0.01
  trans_t_9to9: 0.01
  # 趋势特征权重 - 提升权重
  trend10_b: 0.08
  trend10_g: 0.08
  trend10_k: 0.08
  trend10_t: 0.08
# 🔧 第一阶段优化：统一评分配置，消除不一致
scoring:
  model_weight: 0.7
  normalize_scores: true
  rule_weight: 0.3
  score_clip_range:
    max: 1.0
    min: 0.0
  # 统一双模型权重配置
  score_weights:
    model: 0.7
    rule: 0.3
  # 优化三模型权重配置 - 基于性能分析结果
  score_weights_three:
    mlp: 0.35
    rule: 0.15
    xgb: 0.50
  # 强制使用三模型融合，确保所有模型参与评分
  force_three_models: true
selection:
  diversity_enforce: true
  filter_illegal: true
  min_strategy_representation: 3
  top_n: 30
# 🔧 第一阶段优化：平衡策略权重，提高覆盖率
strategy_paths:
  allow_random_disturbance: true
  max_active_paths: 6  # 增加活跃策略数量
  min_weight_threshold: 0.10  # 降低阈值，允许更多策略参与
  # 重新平衡策略权重，确保多样性
  冷修复: 0.15
  冷热混合: 0.15
  均值回归: 0.20
  多样化处理: 0.25
  极值: 0.15
  爆破: 0.05
  稳定性: 0.05
  组合模式: 0.0
  趋势跟踪: 0.0
  高频周期: 0.0
strategy_selector:
  boost_rate: 0.0
  decay_rate: 0.0
  diversity_factor: 0.0
  enable_diversity: false
  exploration_factor: 0.0
  max_cooldown_allowed: 3
  max_unused_time: 2400
  max_weight: 5.0
  min_weight: 0.05
system:
  candidate_count: 10000  # 候选结构数量，可通过环境变量MAX_CANDIDATES覆盖
  memory_size: 1000
  retrain_interval: 1
