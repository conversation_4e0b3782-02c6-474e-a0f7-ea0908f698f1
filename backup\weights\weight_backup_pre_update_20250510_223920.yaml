# 预测软件全局配置文件
# 集中定义全系统各模块运行所需的静态或初始参数，避免硬编码

# 模型相关配置
model:
  type: xgboost                # 使用的模型类型：xgboost, lightgbm, none
  path: models/xgb_model.json  # 模型加载路径
  use_model: true              # 是否启用模型评分器
  threshold: 0.5               # 模型评分阈值（用于后期筛选或二分决策）

# 特征相关配置
features:
  window_5: 5                  # 5期窗口特征计算
  window_10: 10                # 10期窗口特征计算
  window_30: 30                # 30期窗口特征计算
  use_markov: true             # 是否启用马尔可夫特征计算
  use_cross_position: true     # 是否启用跨位组合频次特征
  normalize_features: true     # 是否对特征值标准化

# 策略路径配置
strategy_paths:
  爆破: 0.3                    # 爆破策略权重
  冷修复: 0.2                  # 冷修复策略权重
  高频周期: 0.25               # 高频周期策略权重
  稳定结构: 0.25               # 稳定结构策略权重
  max_active_paths: 2          # 每轮最多启用几个路径
  min_weight_threshold: 0.1    # 权重低于此值的路径将被禁用（冷却）
  allow_random_disturbance: true # 是否允许策略选择中加入轻微随机性扰动

# 迭代控制配置
iteration:
  max_rounds: 5                # 每期最多允许多少轮迭代
  feedback_trigger_on_zero: true # 命中为0是否触发反馈学习
  early_stop_on_hit: true      # 命中≥1是否提前终止迭代
  feedback_mode: path_tagged   # 反馈方式：simple, weighted, path_tagged

# 评分配置
scoring:
  rule_weight: 0.4             # 规则引擎分数在融合中的占比
  model_weight: 0.6            # 模型评分占比
  normalize_scores: true       # 是否对所有评分进行归一化处理
  score_clip_range:            # 限制评分范围
    min: 0.0
    max: 1.0

# 结构选择配置
selection:
  top_n: 20                    # 每轮最终选出结构的数量
  filter_illegal: true         # 是否自动过滤非法结构（如不是4位）
  diversity_enforce: true      # 是否启用结构多样性控制（如千位分布）

# 路径配置
paths:
  log_dir: logs                # 日志写入目录
  model_dir: models            # 模型文件目录
  feedback_save_path: data/feedback # 命中反馈结果保存路径
  result_save_path: data/results    # 最终预测结果输出路径
  data_path: data/lottery_history.xlsx # 历史数据路径
  memory_path: data/memory     # 记忆存储路径

# 日志配置
logging:
  level: INFO                  # 日志等级（DEBUG, INFO, WARNING, ERROR, CRITICAL）
  log_to_file: true            # 是否写入文件
  log_format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s" # 日志格式

# 其他系统参数
system:
  candidate_count: 1000        # 候选结构数量
  retrain_interval: 5          # 模型重训练间隔（期数）