#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
特征集成模块 - 第二阶段特征工程重构
将优化的特征生成器集成到现有系统中
"""

import os
import sys
import polars as pl
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

try:
    from src.utils.logger import get_logger
    from src.data.optimized_feature_generator import (
        OptimizedFeatureGenerator, 
        FeatureSelector, 
        evaluate_feature_importance,
        generate_optimized_features
    )
    from src.data.feature_generator import Feature, filter_by_round_polars
    logger = get_logger(__name__)
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)


class FeatureIntegrator:
    """特征集成器 - 统一管理新旧特征系统"""
    
    def __init__(self, use_optimized: bool = True):
        self.use_optimized = use_optimized
        self.feature_generator = OptimizedFeatureGenerator()
        self.feature_selector = FeatureSelector()
        
        # 特征质量阈值
        self.min_importance_threshold = 0.1
        self.max_features = 200  # 限制特征数量，防止过拟合
        
    def generate_integrated_features(self, df: pl.DataFrame, current_round: int, 
                                   structures: Optional[List[str]] = None) -> pl.DataFrame:
        """生成集成特征"""
        try:
            if self.use_optimized:
                logger.info("🚀 使用优化特征生成器")
                return self._generate_optimized_features(df, current_round, structures)
            else:
                logger.info("📊 使用传统特征生成器")
                return self._generate_legacy_features(df, current_round, structures)
                
        except Exception as e:
            logger.error(f"❌ 特征生成失败: {e}")
            # 回退到默认特征
            return self._generate_fallback_features(structures)
    
    def _generate_optimized_features(self, df: pl.DataFrame, current_round: int, 
                                   structures: Optional[List[str]] = None) -> pl.DataFrame:
        """生成优化特征"""
        # 使用优化特征生成器
        features_df = generate_optimized_features(df, current_round, structures)
        
        # 特征选择和质量控制
        processed_data = []
        for row in features_df.to_dicts():
            structure = row['structure']
            original_features = row['features']
            
            # 特征选择
            selected_features = self.feature_selector.select_features(original_features)
            
            # 特征重要性评估
            importance_scores = evaluate_feature_importance(selected_features)
            
            # 过滤低重要性特征
            filtered_features = {
                name: value for name, value in selected_features.items()
                if importance_scores.get(name, 0) >= self.min_importance_threshold
            }
            
            # 限制特征数量
            if len(filtered_features) > self.max_features:
                # 按重要性排序，保留前N个特征
                sorted_features = sorted(
                    filtered_features.items(),
                    key=lambda x: importance_scores.get(x[0], 0),
                    reverse=True
                )[:self.max_features]
                filtered_features = dict(sorted_features)
            
            processed_data.append({
                'structure': structure,
                'features': filtered_features
            })
            
        logger.info(f"✅ 优化特征生成完成，平均特征数: {len(processed_data[0]['features']) if processed_data else 0}")
        return pl.DataFrame(processed_data)
    
    def _generate_legacy_features(self, df: pl.DataFrame, current_round: int, 
                                structures: Optional[List[str]] = None) -> pl.DataFrame:
        """生成传统特征（兼容性）"""
        try:
            # 导入传统特征生成器
            from src.data.feature_generator import generate_unified_features_polars
            return generate_unified_features_polars(df, current_round, structures)
        except Exception as e:
            logger.error(f"❌ 传统特征生成失败: {e}")
            return self._generate_fallback_features(structures)
    
    def _generate_fallback_features(self, structures: Optional[List[str]] = None) -> pl.DataFrame:
        """生成回退特征"""
        logger.warning("⚠️ 使用回退特征")
        
        # 基本回退特征
        fallback_features = {
            'freq5_k_0': 0.1, 'freq5_k_1': 0.1, 'freq5_k_2': 0.1,
            'freq5_b_0': 0.1, 'freq5_b_1': 0.1, 'freq5_b_2': 0.1,
            'freq5_t_0': 0.1, 'freq5_t_1': 0.1, 'freq5_t_2': 0.1,
            'freq5_g_0': 0.1, 'freq5_g_1': 0.1, 'freq5_g_2': 0.1,
            'mean_k': 4.5, 'mean_b': 4.5, 'mean_t': 4.5, 'mean_g': 4.5,
            'std_k': 2.87, 'std_b': 2.87, 'std_t': 2.87, 'std_g': 2.87,
        }
        
        if structures is None or not structures:
            return pl.DataFrame([{'structure': '', 'features': fallback_features}])
        
        result_data = []
        for structure in structures:
            result_data.append({'structure': structure, 'features': fallback_features.copy()})
            
        return pl.DataFrame(result_data)
    
    def validate_features(self, features_df: pl.DataFrame) -> bool:
        """验证特征质量"""
        try:
            if features_df.is_empty():
                logger.error("❌ 特征DataFrame为空")
                return False
                
            if 'features' not in features_df.columns:
                logger.error("❌ 缺少features列")
                return False
                
            # 检查特征数量
            sample_features = features_df[0, 'features']
            if len(sample_features) < 10:
                logger.warning(f"⚠️ 特征数量较少: {len(sample_features)}")
                
            # 检查特征值的有效性
            invalid_count = 0
            for name, value in sample_features.items():
                if not isinstance(value, (int, float)) or not (-1000 <= value <= 1000):
                    invalid_count += 1
                    
            if invalid_count > len(sample_features) * 0.1:  # 超过10%的特征无效
                logger.error(f"❌ 无效特征过多: {invalid_count}/{len(sample_features)}")
                return False
                
            logger.info(f"✅ 特征验证通过，特征数量: {len(sample_features)}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 特征验证失败: {e}")
            return False
    
    def get_feature_statistics(self, features_df: pl.DataFrame) -> Dict[str, Any]:
        """获取特征统计信息"""
        try:
            if features_df.is_empty():
                return {}
                
            sample_features = features_df[0, 'features']
            
            # 统计信息
            stats = {
                'total_features': len(sample_features),
                'feature_types': {},
                'value_ranges': {},
                'quality_score': 0.0
            }
            
            # 按类型分组统计
            for name, value in sample_features.items():
                feature_type = name.split('_')[0] if '_' in name else 'other'
                if feature_type not in stats['feature_types']:
                    stats['feature_types'][feature_type] = 0
                stats['feature_types'][feature_type] += 1
                
                # 值范围统计
                if feature_type not in stats['value_ranges']:
                    stats['value_ranges'][feature_type] = {'min': value, 'max': value}
                else:
                    stats['value_ranges'][feature_type]['min'] = min(
                        stats['value_ranges'][feature_type]['min'], value
                    )
                    stats['value_ranges'][feature_type]['max'] = max(
                        stats['value_ranges'][feature_type]['max'], value
                    )
            
            # 计算质量分数
            quality_factors = [
                min(stats['total_features'] / 50, 1.0),  # 特征数量因子
                len(stats['feature_types']) / 10,        # 特征类型多样性因子
                1.0 if stats['total_features'] >= 20 else 0.5  # 最小特征数量因子
            ]
            stats['quality_score'] = sum(quality_factors) / len(quality_factors)
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ 获取特征统计失败: {e}")
            return {}


# 全局特征集成器实例
_feature_integrator = None

def get_feature_integrator(use_optimized: bool = True) -> FeatureIntegrator:
    """获取特征集成器实例"""
    global _feature_integrator
    if _feature_integrator is None or _feature_integrator.use_optimized != use_optimized:
        _feature_integrator = FeatureIntegrator(use_optimized=use_optimized)
    return _feature_integrator


def generate_features_unified(df: pl.DataFrame, current_round: int, 
                            structures: Optional[List[str]] = None,
                            use_optimized: bool = True) -> pl.DataFrame:
    """统一特征生成接口"""
    integrator = get_feature_integrator(use_optimized=use_optimized)
    features_df = integrator.generate_integrated_features(df, current_round, structures)
    
    # 验证特征质量
    if not integrator.validate_features(features_df):
        logger.warning("⚠️ 特征质量验证失败，使用回退特征")
        features_df = integrator._generate_fallback_features(structures)
    
    # 输出特征统计
    stats = integrator.get_feature_statistics(features_df)
    logger.info(f"📊 特征统计: {stats}")
    
    return features_df


if __name__ == "__main__":
    # 测试特征集成
    logger.info("🧪 测试特征集成模块")
    
    # 创建测试数据
    test_data = pl.DataFrame({
        "期号": list(range(1, 21)),
        "千位": [1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0],
        "百位": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        "十位": [9, 8, 7, 6, 5, 4, 3, 2, 1, 0, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0],
        "个位": [5, 5, 5, 5, 5, 6, 6, 6, 6, 6, 7, 7, 7, 7, 7, 8, 8, 8, 8, 8]
    })
    
    # 测试优化特征生成
    features_df = generate_features_unified(test_data, 21, ["1234", "5678"], use_optimized=True)
    logger.info(f"✅ 优化特征生成测试完成")
    
    # 测试传统特征生成（如果可用）
    try:
        features_df_legacy = generate_features_unified(test_data, 21, ["1234", "5678"], use_optimized=False)
        logger.info(f"✅ 传统特征生成测试完成")
    except Exception as e:
        logger.warning(f"⚠️ 传统特征生成不可用: {e}")
    
    logger.info("🎉 特征集成模块测试完成")
