#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化特征生成器 - 第二阶段特征工程重构
基于数据科学原理，移除噪声特征，保留高价值特征
"""

import os
import sys
import numpy as np
import polars as pl
from typing import Dict, List, Any, Optional, Tuple
from pydantic import BaseModel, Field

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

try:
    from src.utils.logger import get_logger
    from src.data.feature_generator import Feature, filter_by_round_polars
    from src.data.number_splitter import SplitRecord
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # 定义基本的Feature类
    class Feature(BaseModel):
        structure: str = ""
        features: Dict[str, float] = Field(default_factory=dict)

logger = get_logger(__name__)


class OptimizedFeatureConfig(BaseModel):
    """优化特征配置"""
    # 核心特征类型（基于统计学和信息论）
    enable_frequency_features: bool = True      # 频率特征
    enable_statistical_features: bool = True    # 统计特征
    enable_temporal_features: bool = True       # 时序特征
    enable_pattern_features: bool = True        # 模式特征
    
    # 移除的噪声特征类型
    disable_fractal_features: bool = True       # 分形特征（噪声过多）
    disable_phase_features: bool = True         # 相位特征（不适用）
    disable_frequency_domain: bool = True       # 频域特征（过度复杂）
    disable_information_theory: bool = True     # 信息论特征（计算复杂）
    
    # 特征窗口大小
    short_window: int = 5    # 短期窗口
    medium_window: int = 10  # 中期窗口
    long_window: int = 20    # 长期窗口


class OptimizedFeatureGenerator:
    """优化的特征生成器"""
    
    def __init__(self, config: Optional[OptimizedFeatureConfig] = None):
        self.config = config or OptimizedFeatureConfig()
        
    def generate_core_features(self, df: pl.DataFrame, current_round: int) -> Dict[str, float]:
        """生成核心特征集合"""
        features = {}
        
        # 筛选历史数据
        history_df = filter_by_round_polars(df, current_round)
        if history_df.is_empty():
            return self._get_default_features()
            
        # 1. 频率特征（最重要）
        if self.config.enable_frequency_features:
            freq_features = self._generate_frequency_features(history_df)
            features.update(freq_features)
            
        # 2. 统计特征
        if self.config.enable_statistical_features:
            stat_features = self._generate_statistical_features(history_df)
            features.update(stat_features)
            
        # 3. 时序特征
        if self.config.enable_temporal_features:
            temporal_features = self._generate_temporal_features(history_df)
            features.update(temporal_features)
            
        # 4. 模式特征
        if self.config.enable_pattern_features:
            pattern_features = self._generate_pattern_features(history_df)
            features.update(pattern_features)
            
        return features
    
    def _generate_frequency_features(self, df: pl.DataFrame) -> Dict[str, float]:
        """生成频率特征（核心特征）"""
        features = {}
        positions = ['千位', '百位', '十位', '个位']
        
        for pos in positions:
            if pos not in df.columns:
                continue
                
            pos_abbr = {'千位': 'k', '百位': 'b', '十位': 't', '个位': 'g'}[pos]
            
            # 短期频率（5期）
            recent_5 = df.tail(self.config.short_window)[pos].value_counts()
            for digit in range(10):
                count = recent_5.filter(pl.col(pos) == digit).select(pl.col("count")).to_numpy()
                freq = count[0] if len(count) > 0 else 0
                features[f"freq5_{pos_abbr}_{digit}"] = float(freq) / self.config.short_window
                
            # 中期频率（10期）
            recent_10 = df.tail(self.config.medium_window)[pos].value_counts()
            for digit in range(10):
                count = recent_10.filter(pl.col(pos) == digit).select(pl.col("count")).to_numpy()
                freq = count[0] if len(count) > 0 else 0
                features[f"freq10_{pos_abbr}_{digit}"] = float(freq) / self.config.medium_window
                
            # 长期频率（20期）
            recent_20 = df.tail(self.config.long_window)[pos].value_counts()
            for digit in range(10):
                count = recent_20.filter(pl.col(pos) == digit).select(pl.col("count")).to_numpy()
                freq = count[0] if len(count) > 0 else 0
                features[f"freq20_{pos_abbr}_{digit}"] = float(freq) / self.config.long_window
                
        return features
    
    def _generate_statistical_features(self, df: pl.DataFrame) -> Dict[str, float]:
        """生成统计特征"""
        features = {}
        positions = ['千位', '百位', '十位', '个位']
        
        for pos in positions:
            if pos not in df.columns:
                continue
                
            pos_abbr = {'千位': 'k', '百位': 'b', '十位': 't', '个位': 'g'}[pos]
            
            # 基本统计量
            pos_data = df[pos].cast(pl.Float64)
            features[f"mean_{pos_abbr}"] = float(pos_data.mean() or 4.5)
            features[f"std_{pos_abbr}"] = float(pos_data.std() or 2.87)
            features[f"var_{pos_abbr}"] = float(pos_data.var() or 8.25)
            
            # 分位数
            quantiles = pos_data.quantile([0.25, 0.5, 0.75])
            features[f"q25_{pos_abbr}"] = float(quantiles[0] or 2.25)
            features[f"median_{pos_abbr}"] = float(quantiles[1] or 4.5)
            features[f"q75_{pos_abbr}"] = float(quantiles[2] or 6.75)
            
            # 偏度和峰度
            features[f"skew_{pos_abbr}"] = float(pos_data.skew() or 0.0)
            features[f"kurt_{pos_abbr}"] = float(pos_data.kurtosis() or -1.2)
            
        return features
    
    def _generate_temporal_features(self, df: pl.DataFrame) -> Dict[str, float]:
        """生成时序特征"""
        features = {}
        positions = ['千位', '百位', '十位', '个位']
        
        for pos in positions:
            if pos not in df.columns:
                continue
                
            pos_abbr = {'千位': 'k', '百位': 'b', '十位': 't', '个位': 'g'}[pos]
            pos_data = df[pos].cast(pl.Float64)
            
            # 滞后特征
            for lag in [1, 2, 3]:
                if len(pos_data) > lag:
                    lagged = pos_data.shift(lag)
                    features[f"lag{lag}_{pos_abbr}"] = float(lagged.tail(1).item() or 4.5)
                    
            # 差分特征
            if len(pos_data) > 1:
                diff1 = pos_data.diff()
                features[f"diff1_{pos_abbr}"] = float(diff1.tail(1).item() or 0.0)
                features[f"diff1_abs_{pos_abbr}"] = float(abs(diff1.tail(1).item() or 0.0))
                
            # 移动平均
            if len(pos_data) >= 3:
                ma3 = pos_data.rolling_mean(window_size=3)
                features[f"ma3_{pos_abbr}"] = float(ma3.tail(1).item() or 4.5)
                
            if len(pos_data) >= 5:
                ma5 = pos_data.rolling_mean(window_size=5)
                features[f"ma5_{pos_abbr}"] = float(ma5.tail(1).item() or 4.5)
                
        return features
    
    def _generate_pattern_features(self, df: pl.DataFrame) -> Dict[str, float]:
        """生成模式特征"""
        features = {}
        positions = ['千位', '百位', '十位', '个位']
        
        # 连续性特征
        for pos in positions:
            if pos not in df.columns:
                continue
                
            pos_abbr = {'千位': 'k', '百位': 'b', '十位': 't', '个位': 'g'}[pos]
            pos_data = df[pos].to_list()
            
            # 最近连续出现次数
            if pos_data:
                last_digit = pos_data[-1]
                consecutive_count = 0
                for i in range(len(pos_data) - 1, -1, -1):
                    if pos_data[i] == last_digit:
                        consecutive_count += 1
                    else:
                        break
                features[f"consecutive_{pos_abbr}"] = float(consecutive_count)
                
            # 缺失期数（冷号特征）
            for digit in range(10):
                missing_periods = 0
                for i in range(len(pos_data) - 1, -1, -1):
                    if pos_data[i] == digit:
                        break
                    missing_periods += 1
                else:
                    missing_periods = len(pos_data)  # 如果没找到，设为总期数
                features[f"missing_{pos_abbr}_{digit}"] = float(missing_periods)
                
        return features
    
    def _get_default_features(self) -> Dict[str, float]:
        """获取默认特征值"""
        features = {}
        positions = ['k', 'b', 't', 'g']
        
        # 默认频率特征
        for pos in positions:
            for digit in range(10):
                features[f"freq5_{pos}_{digit}"] = 0.1
                features[f"freq10_{pos}_{digit}"] = 0.1
                features[f"freq20_{pos}_{digit}"] = 0.1
                features[f"missing_{pos}_{digit}"] = 5.0
                
            # 默认统计特征
            features[f"mean_{pos}"] = 4.5
            features[f"std_{pos}"] = 2.87
            features[f"consecutive_{pos}"] = 1.0
            
        return features


def generate_optimized_features(df: pl.DataFrame, current_round: int, 
                              structures: Optional[List[str]] = None) -> pl.DataFrame:
    """生成优化的特征集合"""
    generator = OptimizedFeatureGenerator()
    
    # 生成基础特征
    base_features = generator.generate_core_features(df, current_round)
    
    if structures is None or not structures:
        # 返回通用特征
        return pl.DataFrame([{"structure": "", "features": base_features}])
    
    # 为每个结构生成特征
    result_data = []
    for structure in structures:
        # 这里可以添加结构特定的特征生成逻辑
        structure_features = base_features.copy()
        
        # 添加结构特定特征
        if len(structure) == 4:
            digits = [int(d) for d in structure]
            structure_features["struct_sum"] = float(sum(digits))
            structure_features["struct_mean"] = float(sum(digits) / 4)
            structure_features["struct_std"] = float(np.std(digits))
            
        result_data.append({"structure": structure, "features": structure_features})
    
    return pl.DataFrame(result_data)


class FeatureSelector:
    """特征选择器 - 基于统计学方法选择最有价值的特征"""

    def __init__(self):
        # 高价值特征类型（基于彩票预测的统计学分析）
        self.high_value_features = [
            'freq5_',    # 短期频率（最重要）
            'freq10_',   # 中期频率
            'missing_',  # 冷号特征
            'consecutive_', # 连续性
            'diff1_',    # 一阶差分
            'ma3_',      # 短期移动平均
            'std_',      # 标准差
        ]

        # 低价值特征类型（噪声较多）
        self.low_value_features = [
            'fractal_',     # 分形特征
            'phase_',       # 相位特征
            'frequency_domain_', # 频域特征
            'information_theory_', # 信息论特征
            'nonlinear_',   # 非线性变换
            'advanced_cross_', # 高级交叉特征
        ]

    def select_features(self, features: Dict[str, float]) -> Dict[str, float]:
        """选择高价值特征，移除噪声特征"""
        selected_features = {}

        for feature_name, feature_value in features.items():
            # 检查是否为高价值特征
            is_high_value = any(feature_name.startswith(prefix)
                              for prefix in self.high_value_features)

            # 检查是否为低价值特征
            is_low_value = any(feature_name.startswith(prefix)
                             for prefix in self.low_value_features)

            # 只保留高价值特征，排除低价值特征
            if is_high_value and not is_low_value:
                selected_features[feature_name] = feature_value
            elif not is_low_value and not any(feature_name.startswith(prefix)
                                            for prefix in self.high_value_features + self.low_value_features):
                # 保留中性特征
                selected_features[feature_name] = feature_value

        return selected_features


def evaluate_feature_importance(features: Dict[str, float]) -> Dict[str, float]:
    """评估特征重要性"""
    importance_scores = {}

    # 特征类型重要性权重
    type_weights = {
        'freq5_': 1.0,      # 短期频率最重要
        'freq10_': 0.9,     # 中期频率
        'freq20_': 0.8,     # 长期频率
        'missing_': 0.85,   # 冷号特征
        'consecutive_': 0.7, # 连续性
        'diff1_': 0.6,      # 差分特征
        'ma3_': 0.5,        # 移动平均
        'ma5_': 0.45,       # 长期移动平均
        'std_': 0.4,        # 标准差
        'mean_': 0.3,       # 均值
        'struct_': 0.2,     # 结构特征
    }

    for feature_name, feature_value in features.items():
        # 基础重要性
        base_importance = 0.1

        # 根据特征类型调整重要性
        for prefix, weight in type_weights.items():
            if feature_name.startswith(prefix):
                base_importance = weight
                break

        # 根据特征值的变异性调整重要性
        abs_value = abs(feature_value)
        if abs_value > 0:
            value_factor = min(abs_value, 2.0)  # 限制最大影响
            importance_scores[feature_name] = base_importance * (1.0 + value_factor * 0.1)
        else:
            importance_scores[feature_name] = base_importance * 0.5

    return importance_scores


if __name__ == "__main__":
    # 测试优化特征生成器
    logger.info("🧪 测试优化特征生成器")

    # 创建测试数据
    test_data = pl.DataFrame({
        "期号": list(range(1, 21)),
        "千位": [1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0],
        "百位": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        "十位": [9, 8, 7, 6, 5, 4, 3, 2, 1, 0, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0],
        "个位": [5, 5, 5, 5, 5, 6, 6, 6, 6, 6, 7, 7, 7, 7, 7, 8, 8, 8, 8, 8]
    })

    # 生成特征
    features_df = generate_optimized_features(test_data, 21, ["1234", "5678"])
    logger.info(f"✅ 生成特征完成，特征数量: {len(features_df[0, 'features'])}")

    # 测试特征选择
    selector = FeatureSelector()
    original_features = features_df[0, 'features']
    selected_features = selector.select_features(original_features)
    logger.info(f"✅ 特征选择完成，保留特征数量: {len(selected_features)}")

    # 测试特征重要性评估
    importance = evaluate_feature_importance(selected_features)
    top_features = sorted(importance.items(), key=lambda x: x[1], reverse=True)[:10]
    logger.info(f"✅ Top 10 重要特征: {[f[0] for f in top_features]}")
