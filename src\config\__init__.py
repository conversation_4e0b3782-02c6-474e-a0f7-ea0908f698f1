#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置包：包含系统配置相关模块。

本包负责加载和处理系统配置信息，包括路径配置、参数配置等。
"""

import logging
import os
from typing import Any, Dict, Optional

import yaml

# 配置日志
logger = logging.getLogger(__name__)

# 默认配置文件路径
DEFAULT_CONFIG_PATH = os.path.join(os.path.dirname(__file__), "..", "config.yaml")


def flatten_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    扁平化配置：将嵌套的配置提升到根级别

    参数:
        config: 原始配置字典

    返回:
        Dict[str, Any]: 扁平化后的配置字典
    """
    flattened = config.copy()

    # 将scoring节点下的配置提升到根级别
    if "scoring" in config:
        scoring_config = config["scoring"]
        if isinstance(scoring_config, dict):
            for key, value in scoring_config.items():
                flattened[key] = value
            logger.info(f"扁平化scoring配置: {list(scoring_config.keys())}")

    # 将system节点下的配置提升到根级别
    if "system" in config:
        system_config = config["system"]
        if isinstance(system_config, dict):
            for key, value in system_config.items():
                flattened[key] = value
            logger.info(f"扁平化system配置: {list(system_config.keys())}")

    # 将strategy_paths节点下的配置提升到根级别（作为strategy_weights）
    if "strategy_paths" in config:
        strategy_config = config["strategy_paths"]
        if isinstance(strategy_config, dict):
            # 提取策略权重（排除配置项）
            strategy_weights = {}
            for key, value in strategy_config.items():
                if (
                    isinstance(value, (int, float))
                    and not key.startswith("_")
                    and not key.startswith("allow")
                    and not key.startswith("max")
                    and not key.startswith("min")
                ):
                    strategy_weights[key] = float(value)

            if strategy_weights:
                flattened["strategy_weights"] = strategy_weights
                logger.info(
                    f"扁平化strategy_weights配置: {list(strategy_weights.keys())}"
                )

    # 将strategy_selector节点下的配置提升到根级别
    if "strategy_selector" in config:
        selector_config = config["strategy_selector"]
        if isinstance(selector_config, dict):
            for key, value in selector_config.items():
                flattened[key] = value
            logger.info(f"扁平化strategy_selector配置: {list(selector_config.keys())}")

    # 将feedback节点下的配置提升到根级别
    if "feedback" in config:
        feedback_config = config["feedback"]
        if isinstance(feedback_config, dict):
            for key, value in feedback_config.items():
                flattened[key] = value
            logger.info(f"扁平化feedback配置: {list(feedback_config.keys())}")

    # 将selection节点下的配置提升到根级别
    if "selection" in config:
        selection_config = config["selection"]
        if isinstance(selection_config, dict):
            for key, value in selection_config.items():
                flattened[key] = value
            logger.info(f"扁平化selection配置: {list(selection_config.keys())}")

    return flattened


def load_config(config_path: Optional[str] = None) -> Dict[str, Any]:
    """
    加载配置文件，支持从默认路径或指定路径加载。

    参数:
        config_path: 配置文件路径，如果为None则使用默认路径

    返回:
        Dict[str, Any]: 配置字典
    """
    # 如果未指定配置文件路径，使用默认路径
    if config_path is None:
        config_path = DEFAULT_CONFIG_PATH

    # 检查配置文件是否存在
    if not os.path.exists(config_path):
        logger.warning(f"配置文件不存在: {config_path}，将使用默认配置")
        return get_default_config()

    try:
        # 读取YAML配置文件
        with open(config_path, "r", encoding="utf-8") as f:
            config = yaml.safe_load(f)

        # 如果配置为空，使用默认配置
        if config is None:
            logger.warning(f"配置文件为空: {config_path}，将使用默认配置")
            return get_default_config()

        logger.info(f"成功加载配置文件: {config_path}")

        # 扁平化配置：将嵌套的配置提升到根级别
        flattened_config = flatten_config(config)
        return flattened_config

    except Exception as e:
        logger.error(f"加载配置文件时发生错误: {e}")
        return get_default_config()


def get_default_config() -> Dict[str, Any]:
    """
    获取默认配置。

    返回:
        Dict[str, Any]: 默认配置字典
    """
    return {
        # 数据相关配置
        "data_path": "历史数据.xlsx",
        # 训练相关配置
        "train_range": (1, 100),
        "model_path": "models/xgboost_model.json",  # 统一使用xgboost_model.json
        # 预测相关配置
        "predict_range": (101, 110),
        # 策略相关配置
        "strategy_weights": {
            "爆破": 0.3,
            "冷修复": 0.2,
            "高频周期": 0.25,
            "稳定结构": 0.25,
        },
        # 🔧 第一阶段优化：统一评分相关配置
        "score_weights": {"rule": 0.3, "model": 0.7},
        "score_weights_three": {"rule": 0.15, "xgb": 0.50, "mlp": 0.35},
        # 候选结构相关配置
        "candidate_count": 10000,  # 默认候选结构数量，可通过环境变量MAX_CANDIDATES覆盖
        "top_n": 30,
        # 其他配置
        "retrain_interval": 1,  # GUI模式：每期都训练（每期都有新数据）
        # 🔧 修复：使用paths.py中的统一路径配置，移除硬编码
        # "feedback_path": "data/feedback",  # 已移除硬编码，使用FEEDBACK_DIR
        "memory_path": "data/memory",
        "analysis_path": "data/analysis",
        "log_path": "logs",
    }


def override_config_with_env(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    使用环境变量覆盖配置。

    参数:
        config: 原始配置字典

    返回:
        Dict[str, Any]: 覆盖后的配置字典
    """
    # 复制原始配置
    new_config = config.copy()

    # 环境变量映射
    env_mapping = {
        "DATA_PATH": "data_path",
        "MODEL_PATH": "model_path",
        "MAX_ITERATIONS": "max_iterations",
        "RETRAIN_INTERVAL": "retrain_interval",
    }

    # 使用环境变量覆盖配置
    for env_var, config_key in env_mapping.items():
        if env_var in os.environ:
            new_config[config_key] = os.environ[env_var]
            logger.info(
                f"使用环境变量 {env_var} 覆盖配置 {config_key}: {new_config[config_key]}"
            )

    # 处理特殊的范围配置
    if "TRAIN_RANGE" in os.environ:
        try:
            train_range = os.environ["TRAIN_RANGE"]
            start, end = map(int, train_range.split("-"))
            new_config["train_range"] = (start, end)
            logger.info(
                f"使用环境变量 TRAIN_RANGE 覆盖配置 train_range: {new_config['train_range']}"
            )
        except (ValueError, AttributeError):
            logger.warning(
                f"环境变量 TRAIN_RANGE 格式错误: {os.environ['TRAIN_RANGE']}"
            )

    if "PREDICT_RANGE" in os.environ:
        try:
            predict_range = os.environ["PREDICT_RANGE"]
            start, end = map(int, predict_range.split("-"))
            new_config["predict_range"] = (start, end)
            logger.info(
                f"使用环境变量 PREDICT_RANGE 覆盖配置 predict_range: {new_config['predict_range']}"
            )
        except (ValueError, AttributeError):
            logger.warning(
                f"环境变量 PREDICT_RANGE 格式错误: {os.environ['PREDICT_RANGE']}"
            )

    return new_config


def get_config(config_path: Optional[str] = None) -> Dict[str, Any]:
    """
    获取配置，支持从文件加载和环境变量覆盖。

    参数:
        config_path: 配置文件路径，如果为None则使用默认路径

    返回:
        Dict[str, Any]: 最终配置字典
    """
    # 加载配置文件
    config = load_config(config_path)

    # 使用环境变量覆盖配置
    config = override_config_with_env(config)

    return config


# 导出默认配置
default_config = get_config()
