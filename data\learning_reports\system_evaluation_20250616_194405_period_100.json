{"timestamp": "2025-06-16T19:44:05.456841", "success_rate": 1.0, "learning_status": "initializing", "strategy_performance": {"rule_based": 1.0, "model_based": 1.0, "hybrid": 1.0, "默认策略": 1.7000000000000006, "高频周期": 1.05, "均值回归": 1.05, "多样化处理": 1.05, "测试策略": 1.05}, "model_performance": {}, "feature_importance": {"position_0_digit_3": 1.1900000000000002, "position_1_digit_1": 1.3450000000000002, "position_2_digit_3": 1.3700000000000003, "position_3_digit_9": 1.2066666666666668, "position_0_digit_0": 1.245, "position_0_digit_1": 1.5283333333333338, "position_0_digit_6": 1.0533333333333335, "position_1_digit_0": 1.3383333333333336, "position_1_digit_2": 1.3200000000000005, "position_1_digit_3": 1.07, "position_1_digit_4": 1.1900000000000002, "position_2_digit_4": 1.1533333333333335, "position_2_digit_5": 1.1116666666666666, "position_2_digit_6": 1.045, "position_2_digit_9": 1.135, "position_3_digit_0": 1.3683333333333336, "position_3_digit_3": 1.1933333333333336, "position_3_digit_7": 1.02, "position_2_digit_0": 1.2500000000000002, "position_3_digit_2": 1.1333333333333335, "position_2_digit_8": 1.145, "position_3_digit_1": 1.2916666666666667, "position_0_digit_9": 1.1, "position_3_digit_4": 1.266666666666667, "position_0_digit_7": 1.05, "position_0_digit_2": 1.1033333333333335, "position_0_digit_5": 1.2200000000000002, "position_1_digit_6": 1.1500000000000001, "position_2_digit_7": 1.1533333333333335, "position_1_digit_8": 1.1666666666666667, "position_3_digit_8": 1.1, "position_2_digit_1": 1.236666666666667, "position_0_digit_4": 1.0166666666666666, "position_0_digit_8": 1.0933333333333335, "position_1_digit_9": 1.02, "position_3_digit_6": 1.02}, "recommendations": ["策略 '默认策略' 表现优异，建议增加其权重", "特征 'position_0_digit_1', 'position_2_digit_3', 'position_3_digit_0' 对预测最为重要，建议优化这些特征的计算方法"], "period_id": 100}