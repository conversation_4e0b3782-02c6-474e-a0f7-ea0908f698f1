# 三模型集成权重配置文件结构设计
# 支持多环境配置和动态加载

# 配置文件版本和元数据
version: "1.0"
config_type: "model_weights"
created_at: "2025-01-01T00:00:00Z"
description: "三模型集成权重配置 - 支持LSTM、XGBoost、MLP模型权重动态配置"

# 环境配置标识
environment: "production"  # 可选值: development, testing, production

# 三模型权重配置
model_weights:
  # 主要模型权重配置
  lstm:
    weight: 0.00
    enabled: false
    description: "LSTM长短期记忆网络模型"
    
  xgboost:
    weight: 10.00
    enabled: true
    description: "XGBoost梯度提升模型"
    
  mlp:
    weight: 0.00
    enabled: false
    description: "MLP多层感知机模型"

# 权重配置验证规则
validation:
  # 权重范围验证
  weight_range:
    min: 0.0
    max: 100.0
    
  # 权重和验证（可选，如果需要归一化）
  normalize_weights: false  # 是否自动归一化权重
  allow_zero_sum: true      # 是否允许权重和为0
  
  # 启用模型数量验证
  min_enabled_models: 1     # 至少启用的模型数量
  max_enabled_models: 3     # 最多启用的模型数量

# 配置加载优先级
# 1. 命令行参数 (--config-file)
# 2. 环境变量 (MODEL_WEIGHTS_CONFIG)
# 3. 环境特定配置文件 (config/weights_{environment}.yaml)
# 4. 默认配置文件 (config/weights_default.yaml)

# 配置热重载设置
reload:
  enabled: true
  watch_file_changes: true
  reload_interval_seconds: 30

# 备份和版本控制
backup:
  enabled: true
  backup_on_change: true
  max_backup_files: 10
  backup_directory: "backup/weights"

# 日志配置
logging:
  log_config_changes: true
  log_level: "INFO"
  log_validation_errors: true
