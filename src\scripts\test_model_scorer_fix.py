#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模型评分器修复验证脚本

本脚本用于验证模型评分器的修复是否有效：
1. 测试备用模型选择逻辑
2. 验证多位置模型回退机制
3. 确保系统不再出现无限循环错误
"""

import os
import sys
import numpy as np
import polars as pl

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))

from src.left_brain.model_scorer import (
    _is_multi_position_model,
    _get_backup_xgboost_model,
    predict_model_scores_polars,
)
from src.brain.brain_core import BrainCore
from src.utils.logger import get_logger

# 配置日志
logger = get_logger("model_scorer_fix_test")


def test_backup_model_selection():
    """测试备用模型选择功能"""
    logger.info("=" * 60)
    logger.info("测试1: 备用模型选择功能")
    logger.info("=" * 60)

    backup_model = _get_backup_xgboost_model()

    if backup_model:
        logger.info(f"✅ 找到备用模型: {backup_model}")

        # 验证模型文件是否存在且可读
        if os.path.exists(backup_model) and os.path.isfile(backup_model):
            logger.info(f"✅ 备用模型文件有效: {backup_model}")
            return True
        else:
            logger.error(f"❌ 备用模型文件无效: {backup_model}")
            return False
    else:
        logger.warning("⚠️ 未找到备用模型")
        return False


def test_multi_position_detection():
    """测试多位置模型检测功能"""
    logger.info("=" * 60)
    logger.info("测试2: 多位置模型检测功能")
    logger.info("=" * 60)

    # 测试多位置模型检测
    multi_position_paths = [
        "models/multi_position",
        "models/multi_position_model.json",
        "models/some_regular_model.json",
    ]

    results = {}
    for path in multi_position_paths:
        is_multi = _is_multi_position_model(path)
        results[path] = is_multi
        status = "✅ 是多位置模型" if is_multi else "❌ 不是多位置模型"
        logger.info(f"  {path}: {status}")

    return results


def test_model_scorer_with_fallback():
    """测试模型评分器的回退机制"""
    logger.info("=" * 60)
    logger.info("测试3: 模型评分器回退机制")
    logger.info("=" * 60)

    # 创建测试数据
    test_structures = ["1234", "5678", "9012"]

    # 创建测试特征DataFrame
    feature_data = []
    for structure in test_structures:
        features = {f"feature_{i}": np.random.rand() for i in range(10)}
        feature_data.append({"structure": structure, "features": features})

    feature_df = pl.DataFrame(feature_data)

    logger.info(f"测试结构: {test_structures}")
    logger.info(f"特征DataFrame形状: {feature_df.shape}")

    # 测试使用多位置模型路径（应该回退到备用模型）
    try:
        model_path = "models/multi_position"  # 这会触发多位置检测和回退
        scores = predict_model_scores_polars(test_structures, feature_df, model_path)

        logger.info("评分结果:")
        for structure, score in scores.items():
            logger.info(f"  {structure}: {score:.6f}")

        if scores and all(isinstance(score, (int, float)) for score in scores.values()):
            logger.info("✅ 模型评分器回退机制正常工作")
            return True
        else:
            logger.error("❌ 评分结果异常")
            return False

    except Exception as e:
        logger.error(f"❌ 模型评分器测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_brain_core_model_selection():
    """测试BrainCore模型选择逻辑"""
    logger.info("=" * 60)
    logger.info("测试4: BrainCore模型选择逻辑")
    logger.info("=" * 60)

    try:
        # 创建BrainCore实例
        config = {
            "model_path": "models/xgboost_model.json",
            "max_candidates": 100,
            "scoring": {"force_three_models": False},
        }

        brain = BrainCore(config)

        # 测试模型选择逻辑
        selected_model = brain._select_model_for_prediction(341)

        logger.info(f"选择的模型路径: {selected_model}")

        # 验证选择的模型是否有效
        if selected_model and os.path.exists(selected_model):
            if os.path.isfile(selected_model):
                logger.info("✅ BrainCore选择了有效的模型文件")
                return True
            else:
                logger.warning(f"⚠️ BrainCore选择了目录而不是文件: {selected_model}")
                return False
        else:
            logger.error(f"❌ BrainCore选择了无效的模型: {selected_model}")
            return False

    except Exception as e:
        logger.error(f"❌ BrainCore测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    logger.info("🚀 开始模型评分器修复验证")
    logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    test_results = {}

    # 测试1: 备用模型选择
    test_results["backup_model"] = test_backup_model_selection()

    # 测试2: 多位置模型检测
    test_results["multi_position_detection"] = test_multi_position_detection()

    # 测试3: 模型评分器回退机制
    test_results["scorer_fallback"] = test_model_scorer_with_fallback()

    # 测试4: BrainCore模型选择
    test_results["brain_model_selection"] = test_brain_core_model_selection()

    # 汇总结果
    logger.info("=" * 60)
    logger.info("测试结果汇总")
    logger.info("=" * 60)

    all_passed = True
    for test_name, result in test_results.items():
        if isinstance(result, bool):
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"{test_name}: {status}")
            if not result:
                all_passed = False
        else:
            logger.info(f"{test_name}: {result}")

    if all_passed:
        logger.info("=" * 60)
        logger.info("🎉 所有测试通过！模型评分器修复成功")
        logger.info("=" * 60)
        logger.info("修复效果:")
        logger.info("✅ 备用模型选择机制正常工作")
        logger.info("✅ 多位置模型回退逻辑修复")
        logger.info("✅ 避免了无限循环错误")
        logger.info("✅ BrainCore模型选择逻辑正常")
    else:
        logger.error("=" * 60)
        logger.error("❌ 部分测试失败，需要进一步检查")
        logger.error("=" * 60)

    return all_passed


if __name__ == "__main__":
    from datetime import datetime

    success = main()
    sys.exit(0 if success else 1)
