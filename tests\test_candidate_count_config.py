"""
候选结构数量配置测试

验证新的统一配置管理器和候选结构数量配置的正确性
"""

import os
import pytest
import tempfile
from unittest.mock import patch, MagicMock

# 测试统一配置管理器
def test_unified_config_manager():
    """测试统一配置管理器的基本功能"""
    from src.config.unified_config_manager import UnifiedConfigManager
    
    manager = UnifiedConfigManager()
    
    # 测试默认值
    default_value = manager.get_parameter("system", "candidate_count", 5000)
    assert default_value == 5000
    
    # 测试在线学习更新
    manager.register_online_learning_update(
        "system", "candidate_count", 8000, "测试更新"
    )
    
    updated_value = manager.get_parameter("system", "candidate_count", 5000)
    assert updated_value == 8000


def test_get_candidate_count_function():
    """测试get_candidate_count函数的优先级逻辑"""
    from src.config.unified_config_manager import get_candidate_count
    
    # 测试运行时参数优先级最高
    count = get_candidate_count(count=7500)
    assert count == 7500
    
    # 测试默认值
    count = get_candidate_count(default=12000)
    assert count == 12000


def test_environment_variable_override():
    """测试环境变量覆盖功能"""
    from src.config.unified_config_manager import get_candidate_count
    
    # 模拟环境变量
    with patch.dict(os.environ, {'MAX_CANDIDATES': '15000'}):
        count = get_candidate_count()
        assert count == 15000


def test_config_file_integration():
    """测试配置文件集成"""
    from src.config import get_config
    
    config = get_config()
    
    # 验证新的默认值
    assert config.get("candidate_count") == 10000


def test_candidate_generator_integration():
    """测试候选结构生成器集成"""
    from src.right_brain.candidate_generator import generate_candidates
    from src.data.number_splitter import SplitRecord
    from src.data.feature_generator import Feature
    
    # 创建模拟数据
    mock_data = [
        SplitRecord(period=1, b=1, g=2, k=3, t=4),
        SplitRecord(period=2, b=2, g=3, k=4, t=5),
    ]
    
    mock_features = Feature(features={
        "test_feature_1": 0.5,
        "test_feature_2": 0.8,
    })
    
    # 测试默认配置
    with patch('src.right_brain.structure_builder.build_structures') as mock_build:
        mock_build.return_value = ["1234", "5678", "9012"]
        
        candidates = generate_candidates(
            data=mock_data,
            current_round=3,
            features=mock_features,
            count=None  # 使用默认配置
        )
        
        # 验证调用了build_structures
        assert mock_build.called
        
    # 测试运行时参数覆盖
    with patch('src.right_brain.structure_builder.build_structures') as mock_build:
        mock_build.return_value = ["1234", "5678"]
        
        candidates = generate_candidates(
            data=mock_data,
            current_round=3,
            features=mock_features,
            count=2000  # 运行时指定
        )
        
        # 验证使用了指定的count参数
        call_args = mock_build.call_args
        assert call_args[1]['count'] == 2000


def test_structure_builder_integration():
    """测试结构构建器集成"""
    from src.right_brain.structure_builder import build_structures
    from src.data.number_splitter import SplitRecord
    from src.data.feature_generator import Feature
    
    # 创建模拟数据
    mock_data = [
        SplitRecord(period=1, b=1, g=2, k=3, t=4),
        SplitRecord(period=2, b=2, g=3, k=4, t=5),
    ]
    
    mock_features = Feature(features={
        "test_feature_1": 0.5,
        "test_feature_2": 0.8,
    })
    
    # 测试使用新的配置管理器
    with patch('src.right_brain.structure_builder._build_explosion_structures') as mock_explosion:
        mock_explosion.return_value = ["1234", "5678", "9012"]
        
        structures = build_structures(
            data=mock_data,
            current_round=3,
            features=mock_features,
            strategy="爆破",
            count=None  # 使用默认配置
        )
        
        # 验证调用了策略函数
        assert mock_explosion.called


def test_diversifier_integration():
    """测试多样化器集成"""
    from src.right_brain.diversifier import diversify_structures
    
    base_structures = ["1234", "5678", "9012"]
    
    # 测试使用新的配置管理器
    with patch('random.choice') as mock_choice:
        mock_choice.return_value = "1"
        
        diversified = diversify_structures(
            base_structures=base_structures,
            target_count=None,  # 使用默认配置
            mutation_rate=0.1
        )
        
        # 验证返回了结构列表
        assert isinstance(diversified, list)


def test_brain_core_integration():
    """测试大脑核心集成"""
    from src.brain.brain_core import BrainCore
    
    # 创建模拟的BrainCore实例
    brain = BrainCore()
    brain.config = {"candidate_count": 8000}
    
    # 测试新的配置获取逻辑
    with patch('src.config.unified_config_manager.get_candidate_count') as mock_get_count:
        mock_get_count.return_value = 8000
        
        # 模拟predict方法中的配置获取
        from src.config.unified_config_manager import get_candidate_count
        max_candidates = get_candidate_count(
            count=brain.config.get("max_candidates"),
            default=brain.config.get("candidate_count", 10000)
        )
        
        assert max_candidates == 8000


def test_performance_compatibility():
    """测试性能兼容性"""
    import time
    import psutil
    from src.config.unified_config_manager import get_candidate_count
    
    # 测试配置获取性能
    start_time = time.time()
    
    for _ in range(1000):
        count = get_candidate_count()
    
    end_time = time.time()
    
    # 配置获取应该很快（小于1秒）
    assert (end_time - start_time) < 1.0
    
    # 测试内存使用
    process = psutil.Process()
    initial_memory = process.memory_info().rss
    
    # 创建大量配置管理器实例
    managers = []
    for _ in range(100):
        from src.config.unified_config_manager import get_unified_config_manager
        managers.append(get_unified_config_manager())
    
    final_memory = process.memory_info().rss
    memory_increase = final_memory - initial_memory
    
    # 内存增长应该很小（小于10MB）
    assert memory_increase < 10 * 1024 * 1024


def test_team_collaboration_config():
    """测试团队协作配置"""
    from src.config.unified_config_manager import get_candidate_count
    
    # 测试开发环境配置
    with patch.dict(os.environ, {'MAX_CANDIDATES': '5000'}):
        dev_count = get_candidate_count()
        assert dev_count == 5000
    
    # 测试生产环境配置
    with patch.dict(os.environ, {'MAX_CANDIDATES': '10000'}):
        prod_count = get_candidate_count()
        assert prod_count == 10000
    
    # 测试测试环境配置
    with patch.dict(os.environ, {'MAX_CANDIDATES': '1000'}):
        test_count = get_candidate_count()
        assert test_count == 1000


def test_backward_compatibility():
    """测试向后兼容性"""
    # 测试旧的配置方式仍然有效
    from src.config import get_config
    
    config = get_config()
    old_style_count = config.get("candidate_count", 3500)
    
    # 新的默认值应该是10000
    assert old_style_count == 10000
    
    # 测试旧的函数调用方式
    from src.right_brain.candidate_generator import generate_candidates
    from src.data.number_splitter import SplitRecord
    from src.data.feature_generator import Feature
    
    mock_data = [SplitRecord(period=1, b=1, g=2, k=3, t=4)]
    mock_features = Feature(features={"test": 0.5})
    
    # 这种调用方式应该仍然有效
    with patch('src.right_brain.structure_builder.build_structures') as mock_build:
        mock_build.return_value = ["1234"]
        
        candidates = generate_candidates(mock_data, 1, mock_features)
        assert isinstance(candidates, list)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
