#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置验证和修复工具 - 第一阶段优化
确保所有配置文件的一致性和正确性
"""

import os
import sys
import yaml
import json
from typing import Dict, Any, List, Tuple

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

try:
    from src.utils.logger import get_logger
    logger = get_logger(__name__)
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)


class ConfigValidator:
    """配置验证和修复工具"""
    
    def __init__(self):
        self.project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        self.config_files = {
            'main_config': os.path.join(self.project_root, 'src', 'config.yaml'),
            'model_params': os.path.join(self.project_root, 'models', 'model_params.json'),
            'constants': os.path.join(self.project_root, 'src', 'utils', 'constants.py'),
        }
        
        # 标准配置值
        self.standard_config = {
            'score_weights_three': {'rule': 0.15, 'xgb': 0.50, 'mlp': 0.35},
            'score_weights': {'rule': 0.3, 'model': 0.7},
            'xgboost_params': {
                'learning_rate': 0.02,
                'max_depth': 6,
                'n_estimators': 2500,
                'scale_pos_weight': 5.0,
                'colsample_bytree': 0.85,
                'subsample': 0.85,
                'gamma': 0.05,
                'min_child_weight': 3.0,
                'reg_alpha': 0.01,
                'reg_lambda': 0.05
            },
            'mlp_params': {
                'batch_size': 64,
                'dropout_rate': 0.3,
                'epochs': 150,
                'hidden_layers': [512, 256, 128, 64],
                'learning_rate': 0.0005,
                'l1_reg': 0.001,
                'l2_reg': 0.01
            }
        }
    
    def validate_all_configs(self) -> Dict[str, List[str]]:
        """验证所有配置文件"""
        issues = {}
        
        # 验证主配置文件
        main_issues = self._validate_main_config()
        if main_issues:
            issues['main_config'] = main_issues
            
        # 验证模型参数文件
        model_issues = self._validate_model_params()
        if model_issues:
            issues['model_params'] = model_issues
            
        # 验证常量文件
        const_issues = self._validate_constants()
        if const_issues:
            issues['constants'] = const_issues
            
        return issues
    
    def _validate_main_config(self) -> List[str]:
        """验证主配置文件"""
        issues = []
        config_path = self.config_files['main_config']
        
        if not os.path.exists(config_path):
            issues.append(f"配置文件不存在: {config_path}")
            return issues
            
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                
            # 检查评分权重
            if 'scoring' in config:
                scoring = config['scoring']
                if 'score_weights_three' in scoring:
                    current = scoring['score_weights_three']
                    expected = self.standard_config['score_weights_three']
                    if current != expected:
                        issues.append(f"score_weights_three不一致: {current} != {expected}")
                        
            # 检查XGBoost参数
            if 'model' in config and 'xgboost_params' in config['model']:
                current_params = config['model']['xgboost_params']
                expected_params = self.standard_config['xgboost_params']
                for key, expected_value in expected_params.items():
                    if key not in current_params:
                        issues.append(f"缺少XGBoost参数: {key}")
                    elif current_params[key] != expected_value:
                        issues.append(f"XGBoost参数{key}不一致: {current_params[key]} != {expected_value}")
                        
        except Exception as e:
            issues.append(f"读取配置文件失败: {e}")
            
        return issues
    
    def _validate_model_params(self) -> List[str]:
        """验证模型参数文件"""
        issues = []
        config_path = self.config_files['model_params']
        
        if not os.path.exists(config_path):
            issues.append(f"模型参数文件不存在: {config_path}")
            return issues
            
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                params = json.load(f)
                
            expected_params = self.standard_config['xgboost_params']
            for key, expected_value in expected_params.items():
                if key not in params:
                    issues.append(f"模型参数文件缺少参数: {key}")
                elif params[key] != expected_value:
                    issues.append(f"模型参数{key}不一致: {params[key]} != {expected_value}")
                    
        except Exception as e:
            issues.append(f"读取模型参数文件失败: {e}")
            
        return issues
    
    def _validate_constants(self) -> List[str]:
        """验证常量文件"""
        issues = []
        # 这里可以添加对constants.py的验证逻辑
        # 由于是Python文件，需要特殊处理
        return issues
    
    def fix_all_configs(self) -> bool:
        """修复所有配置文件"""
        try:
            # 修复模型参数文件
            self._fix_model_params()
            logger.info("✅ 模型参数文件修复完成")
            
            # 验证修复结果
            issues = self.validate_all_configs()
            if not issues:
                logger.info("✅ 所有配置文件验证通过")
                return True
            else:
                logger.warning(f"⚠️ 仍存在配置问题: {issues}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 修复配置文件失败: {e}")
            return False
    
    def _fix_model_params(self):
        """修复模型参数文件"""
        config_path = self.config_files['model_params']
        
        # 确保目录存在
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        # 写入标准参数
        standard_params = self.standard_config['xgboost_params'].copy()
        standard_params.update({
            'objective': 'binary:logistic',
            'eval_metric': 'logloss',
            'random_state': 42
        })
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(standard_params, f, indent=4, ensure_ascii=False)
            
        logger.info(f"✅ 模型参数文件已更新: {config_path}")


def validate_and_fix_configs():
    """验证和修复所有配置文件"""
    validator = ConfigValidator()
    
    logger.info("🔍 开始验证配置文件...")
    issues = validator.validate_all_configs()
    
    if issues:
        logger.warning(f"⚠️ 发现配置问题: {issues}")
        logger.info("🔧 开始修复配置文件...")
        success = validator.fix_all_configs()
        if success:
            logger.info("✅ 配置文件修复完成")
        else:
            logger.error("❌ 配置文件修复失败")
    else:
        logger.info("✅ 所有配置文件验证通过")
    
    return not bool(issues)


if __name__ == "__main__":
    validate_and_fix_configs()
