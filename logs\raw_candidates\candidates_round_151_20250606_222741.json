{"round": 151, "timestamp": "2025-06-06T22:27:41.020044", "count": 100, "expected_count": 100, "candidates": ["6673", "0313", "5686", "4465", "8915", "5945", "3394", "5678", "6263", "8411", "1433", "9189", "4286", "7709", "1000", "9137", "0088", "4793", "3101", "9170", "3344", "0352", "9343", "3538", "6218", "9937", "5464", "5174", "2872", "5643", "1208", "3466", "0089", "8011", "1264", "5765", "5545", "1780", "2460", "8698", "5766", "5018", "3447", "9900", "1101", "5537", "9015", "0118", "6565", "0303", "9939", "0696", "6877", "5976", "5778", "7420", "6264", "5567", "4040", "2337", "2877", "1312", "1199", "2949", "9810", "3997", "4668", "8911", "5167", "9974", "6262", "6663", "9421", "7627", "5291", "7133", "1090", "2046", "0867", "4393", "1806", "7281", "7921", "8003", "1898", "6557", "4304", "8191", "9767", "3358", "3140", "3414", "3032", "0407", "0532", "4567", "1248", "4021", "9919", "0998"], "candidates_with_strategy": [{"structure": "6673", "strategy": "均值回归"}, {"structure": "0313", "strategy": "高频周期"}, {"structure": "5686", "strategy": "均值回归"}, {"structure": "4465", "strategy": "高频周期"}, {"structure": "8915", "strategy": "极值"}, {"structure": "5945", "strategy": "均值回归"}, {"structure": "3394", "strategy": "高频周期"}, {"structure": "5678", "strategy": "均值回归"}, {"structure": "6263", "strategy": "多样化处理"}, {"structure": "8411", "strategy": "极值"}, {"structure": "1433", "strategy": "高频周期"}, {"structure": "9189", "strategy": "极值"}, {"structure": "4286", "strategy": "多样化处理"}, {"structure": "7709", "strategy": "多样化处理"}, {"structure": "1000", "strategy": "极值"}, {"structure": "9137", "strategy": "高频周期"}, {"structure": "0088", "strategy": "极值"}, {"structure": "4793", "strategy": "多样化处理"}, {"structure": "3101", "strategy": "高频周期"}, {"structure": "9170", "strategy": "极值"}, {"structure": "3344", "strategy": "高频周期"}, {"structure": "0352", "strategy": "高频周期"}, {"structure": "9343", "strategy": "多样化处理"}, {"structure": "3538", "strategy": "均值回归"}, {"structure": "6218", "strategy": "均值回归"}, {"structure": "9937", "strategy": "多样化处理"}, {"structure": "5464", "strategy": "均值回归"}, {"structure": "5174", "strategy": "高频周期"}, {"structure": "2872", "strategy": "多样化处理"}, {"structure": "5643", "strategy": "均值回归"}, {"structure": "1208", "strategy": "多样化处理"}, {"structure": "3466", "strategy": "均值回归"}, {"structure": "0089", "strategy": "极值"}, {"structure": "8011", "strategy": "极值"}, {"structure": "1264", "strategy": "高频周期"}, {"structure": "5765", "strategy": "均值回归"}, {"structure": "5545", "strategy": "均值回归"}, {"structure": "1780", "strategy": "极值"}, {"structure": "2460", "strategy": "高频周期"}, {"structure": "8698", "strategy": "极值"}, {"structure": "5766", "strategy": "均值回归"}, {"structure": "5018", "strategy": "极值"}, {"structure": "3447", "strategy": "多样化处理"}, {"structure": "9900", "strategy": "极值"}, {"structure": "1101", "strategy": "极值"}, {"structure": "5537", "strategy": "均值回归"}, {"structure": "9015", "strategy": "极值"}, {"structure": "0118", "strategy": "极值"}, {"structure": "6565", "strategy": "均值回归"}, {"structure": "0303", "strategy": "高频周期"}, {"structure": "9939", "strategy": "多样化处理"}, {"structure": "0696", "strategy": "多样化处理"}, {"structure": "6877", "strategy": "均值回归"}, {"structure": "5976", "strategy": "多样化处理"}, {"structure": "5778", "strategy": "均值回归"}, {"structure": "7420", "strategy": "均值回归"}, {"structure": "6264", "strategy": "均值回归"}, {"structure": "5567", "strategy": "均值回归"}, {"structure": "4040", "strategy": "高频周期"}, {"structure": "2337", "strategy": "高频周期"}, {"structure": "2877", "strategy": "多样化处理"}, {"structure": "1312", "strategy": "高频周期"}, {"structure": "1199", "strategy": "极值"}, {"structure": "2949", "strategy": "高频周期"}, {"structure": "9810", "strategy": "极值"}, {"structure": "3997", "strategy": "高频周期"}, {"structure": "4668", "strategy": "均值回归"}, {"structure": "8911", "strategy": "极值"}, {"structure": "5167", "strategy": "多样化处理"}, {"structure": "9974", "strategy": "多样化处理"}, {"structure": "6262", "strategy": "多样化处理"}, {"structure": "6663", "strategy": "均值回归"}, {"structure": "9421", "strategy": "多样化处理"}, {"structure": "7627", "strategy": "均值回归"}, {"structure": "5291", "strategy": "多样化处理"}, {"structure": "7133", "strategy": "多样化处理"}, {"structure": "1090", "strategy": "极值"}, {"structure": "2046", "strategy": "多样化处理"}, {"structure": "0867", "strategy": "多样化处理"}, {"structure": "4393", "strategy": "多样化处理"}, {"structure": "1806", "strategy": "极值"}, {"structure": "7281", "strategy": "多样化处理"}, {"structure": "7921", "strategy": "多样化处理"}, {"structure": "8003", "strategy": "极值"}, {"structure": "1898", "strategy": "极值"}, {"structure": "6557", "strategy": "均值回归"}, {"structure": "4304", "strategy": "高频周期"}, {"structure": "8191", "strategy": "极值"}, {"structure": "9767", "strategy": "均值回归"}, {"structure": "3358", "strategy": "高频周期"}, {"structure": "3140", "strategy": "高频周期"}, {"structure": "3414", "strategy": "高频周期"}, {"structure": "3032", "strategy": "高频周期"}, {"structure": "0407", "strategy": "高频周期"}, {"structure": "0532", "strategy": "高频周期"}, {"structure": "4567", "strategy": "均值回归"}, {"structure": "1248", "strategy": "多样化处理"}, {"structure": "4021", "strategy": "高频周期"}, {"structure": "9919", "strategy": "极值"}, {"structure": "0998", "strategy": "极值"}], "strategy_statistics": {"均值回归": 25, "高频周期": 25, "极值": 25, "多样化处理": 25}, "metadata": {"generation_method": "strategy_based_with_tracking", "description": "原始候选结构（含策略追踪） - 第151期预测", "format": "4位数字字符串列表，包含策略来源信息"}}